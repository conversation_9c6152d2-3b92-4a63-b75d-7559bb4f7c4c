<p align="center">
  <a href="http://nestjs.com/" target="blank"><img src="https://nestjs.com/img/logo-small.svg" width="200" alt="Nest Logo" /></a>
</p>

[circleci-image]: https://img.shields.io/circleci/build/github/nestjs/nest/master?token=abc123def456
[circleci-url]: https://circleci.com/gh/nestjs/nest

  <p align="center">A progressive <a href="http://nodejs.org" target="_blank">Node.js</a> framework for building efficient and scalable server-side applications.</p>
    <p align="center">
<a href="https://www.npmjs.com/~nestjscore" target="_blank"><img src="https://img.shields.io/npm/v/@nestjs/core.svg" alt="NPM Version" /></a>
<a href="https://www.npmjs.com/~nestjscore" target="_blank"><img src="https://img.shields.io/npm/l/@nestjs/core.svg" alt="Package License" /></a>
<a href="https://www.npmjs.com/~nestjscore" target="_blank"><img src="https://img.shields.io/npm/dm/@nestjs/common.svg" alt="NPM Downloads" /></a>
<a href="https://circleci.com/gh/nestjs/nest" target="_blank"><img src="https://img.shields.io/circleci/build/github/nestjs/nest/master" alt="CircleCI" /></a>
<a href="https://coveralls.io/github/nestjs/nest?branch=master" target="_blank"><img src="https://coveralls.io/repos/github/nestjs/nest/badge.svg?branch=master#9" alt="Coverage" /></a>
<a href="https://discord.gg/G7Qnnhy" target="_blank"><img src="https://img.shields.io/badge/discord-online-brightgreen.svg" alt="Discord"/></a>
<a href="https://opencollective.com/nest#backer" target="_blank"><img src="https://opencollective.com/nest/backers/badge.svg" alt="Backers on Open Collective" /></a>
<a href="https://opencollective.com/nest#sponsor" target="_blank"><img src="https://opencollective.com/nest/sponsors/badge.svg" alt="Sponsors on Open Collective" /></a>
  <a href="https://paypal.me/kamilmysliwiec" target="_blank"><img src="https://img.shields.io/badge/Donate-PayPal-ff3f59.svg"/></a>
    <a href="https://opencollective.com/nest#sponsor"  target="_blank"><img src="https://img.shields.io/badge/Support%20us-Open%20Collective-41B883.svg" alt="Support us"></a>
  <a href="https://twitter.com/nestframework" target="_blank"><img src="https://img.shields.io/twitter/follow/nestframework.svg?style=social&label=Follow"></a>
</p>
  <!--[![Backers on Open Collective](https://opencollective.com/nest/backers/badge.svg)](https://opencollective.com/nest#backer)
  [![Sponsors on Open Collective](https://opencollective.com/nest/sponsors/badge.svg)](https://opencollective.com/nest#sponsor)-->

## Description

[Nest](https://github.com/nestjs/nest) framework TypeScript starter repository.

## Installation

```bash
$ pnpm install
```

## Running the app

```bash
# development
$ pnpm run start

# watch mode
$ pnpm run start:dev

# production mode
$ pnpm run start:prod
```

## Test

```bash
# unit tests
$ pnpm run test

# e2e tests
$ pnpm run test:e2e

# test coverage
$ pnpm run test:cov
```

## Support

Nest is an MIT-licensed open source project. It can grow thanks to the sponsors and support by the amazing backers. If you'd like to join them, please [read more here](https://docs.nestjs.com/support).

## Stay in touch

- Author - [Kamil Myśliwiec](https://kamilmysliwiec.com)
- Website - [https://nestjs.com](https://nestjs.com/)
- Twitter - [@nestframework](https://twitter.com/nestframework)

## License

Nest is [MIT licensed](LICENSE).

## generate auto model controller service

`nest g res users`

## Order status Magento

https://omsdocs.magento.com/features-processes/order-status/?itm_source=oms-en&itm_medium=search_page&itm_campaign=federated_search&itm_term=cod

## In store pickup

https://omsdocs.magento.com/userguides/sales/in-store-pickup/

## Dữ liệu tỉnh thành phố

https://github.com/ThangLeQuoc/vietnamese-provinces-database/blob/master/README_vi.md

## Mongo

Command
mongosh
use admin
show collections
db.products.deleteMany({})
db.getCollection('banner-sections').deleteMany({})

`db.createUser({
  user: "ws-admin",
  pwd: passwordPrompt(),  // Use passwordPrompt() for security, or provide a plain text password
  roles: [
    { role: "readWriteAnyDatabase", db: "admin" }
  ]
})
`
pass: E8jnaX8Axt

create admin role
`db.createUser({
  user: "admin",
  pwd: passwordPrompt(),  // Prompts for the password securely
  roles: [
    { role: "userAdminAnyDatabase", db: "admin" },  // Full user admin rights
    { role: "dbAdminAnyDatabase", db: "admin" },    // Database admin rights
    { role: "readWriteAnyDatabase", db: "admin" }   // Read/write across all databases
  ]
})`

pass: nGS5Fjg74d

`db.updateUser("ws-admin", {pwd: "E8jnaX8Axt" }) `

Remote access Mongo

https://www.digitalocean.com/community/tutorials/how-to-configure-remote-access-for-mongodb-on-ubuntu-20-04

# Terminal access

mongosh --username ws-admin --password E8jnaX8Axt --authenticationDatabase admin --host 0.0.0.0 --port 27017 wstore-dev

# Should push package-lock?

https://stackoverflow.com/questions/44206782/do-i-commit-the-package-lock-json-file-created-by-npm-5

# Docker

https://www.mongodb.com/docs/manual/tutorial/install-mongodb-community-with-docker/

# Docker Compose

docker-compose down -v
docker-compose up -d

#Config transaction
Tạo key để các mongo db kết nối vs nhau
openssl rand -base64 756 > mongo-keyfile

Khởi tạo Replica Set lại
Sau khi container chạy, vào mongo1 để thiết lập Replica Set:

docker exec -it mongo1 mongosh --username root --password root
Chạy lệnh này trong mongosh:

`rs.initiate({
_id: "rs0",
members: [
{ _id: 0, host: "mongo1:27017" },
{ _id: 1, host: "mongo2:27017" }
]
})`

Sau đó kiểm tra Replica Set:

rs.status()
