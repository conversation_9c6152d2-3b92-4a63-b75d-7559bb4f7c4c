import {
  Call<PERSON><PERSON><PERSON>,
  ExecutionContext,
  Injectable,
  NestInterceptor,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { ClassConstructor, plainToInstance } from 'class-transformer';
import { Observable, map } from 'rxjs';

@Injectable()
export class SerializerInterceptor implements NestInterceptor {
  constructor(private readonly reflector: Reflector) {}
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const dto = this.reflector.getAllAndOverride('DTO', [
      context.getClass(),
      context.getHandler(),
    ]) as ClassConstructor<any>;

    return next.handle().pipe(
      map((result) => {
        return {
          data: plainToInstance(dto, result.data || result, {
            excludeExtraneousValues: true,
            enableImplicitConversion: true,
          }),
          metadata: result.metadata,
        };
      }),
    );
  }
}
