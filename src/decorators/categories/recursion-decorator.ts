import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallHandler,
} from '@nestjs/common';
import { Observable, map } from 'rxjs';
import { recursionCategories } from '../../utils/recursion';

@Injectable()
export class RecursionCategoryInterceptor implements NestInterceptor {
  constructor() {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    return next.handle().pipe(
      map((rawData) => {
        rawData.data = recursionCategories(rawData.data);
        return rawData;
      }),
    );
  }
}
