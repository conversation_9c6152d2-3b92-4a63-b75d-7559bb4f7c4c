import {
  ValidatorConstraint,
  ValidatorConstraintInterface,
} from 'class-validator';

@ValidatorConstraint({ name: 'isStringOrStringArray', async: false })
export class IsStringOrStringArray implements ValidatorConstraintInterface {
  validate(value: string[] | string): boolean {
    return (
      typeof value === 'string' ||
      (Array.isArray(value) && value.every((item) => typeof item === 'string'))
    );
  }

  defaultMessage(): string {
    return 'Images must be either a string or an array of strings';
  }
}
