import {
  registerDecorator,
  ValidationOptions,
  ValidationArguments,
} from 'class-validator';
import { ProductType } from '../../utils/enums';

export function IsParentIdValid(validationOptions?: ValidationOptions) {
  return (object: any, propertyName: string) => {
    registerDecorator({
      name: 'isParentIdValid',
      target: object.constructor,
      propertyName,
      options: validationOptions,
      validator: {
        validate: (value: string | null, args: ValidationArguments) => {
          const { productType } = args.object as Record<string, unknown>;
          return !(
            [ProductType.parent, ProductType.combo].includes(
              productType as ProductType,
            ) && value !== null
          );
        },
        defaultMessage: () =>
          `parentId must be null when productType is 'parent' or 'combo'.`,
      },
    });
  };
}
