import { registerDecorator, ValidationOptions } from 'class-validator';

export function IsPhoneNumber(validationOptions?: ValidationOptions) {
  return function (object: any, propertyName: string) {
    registerDecorator({
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      constraints: [],
      validator: {
        validate(phoneNumber: string) {
          const vietnamPhoneRegex = /^(03|05|07|08|09)\d{8}$/;
          return (
            typeof phoneNumber === 'string' &&
            vietnamPhoneRegex.test(phoneNumber)
          );
        },

        defaultMessage() {
          return 'Phone number must be a valid Vietnamese number!';
        },
      },
    });
  };
}
