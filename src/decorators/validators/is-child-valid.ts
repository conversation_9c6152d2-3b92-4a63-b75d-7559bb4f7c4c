import {
  registerDecorator,
  ValidationOptions,
  ValidationArguments,
} from 'class-validator';
import { ProductType } from '../../utils/enums';

export function IsChildsValid(validationOptions?: ValidationOptions) {
  return (object: any, propertyName: string): void => {
    registerDecorator({
      name: 'isChildsValid',
      target: object.constructor,
      propertyName,
      options: validationOptions,
      validator: {
        validate: (value: unknown, args: ValidationArguments): boolean => {
          const { productType } = args.object as { productType: string };
          return !(
            productType === ProductType.single &&
            Array.isArray(value) &&
            value.length > 0
          );
        },
        defaultMessage: (): string =>
          `Childs array must be empty when productType is 'single'.`,
      },
    });
  };
}
