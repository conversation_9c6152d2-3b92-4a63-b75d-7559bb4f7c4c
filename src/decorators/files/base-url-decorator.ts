import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallHandler,
} from '@nestjs/common';
import { Observable, map } from 'rxjs';
import { ConfigService } from '@nestjs/config';
import { getImageToDb } from '../../utils/image';
import { Reflector } from '@nestjs/core';

@Injectable()
export class AddBaseUrlInterceptor implements NestInterceptor {
  constructor(private readonly configService: ConfigService) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const reflector = new Reflector();
    const controllerClass = context.getClass();
    const prefix = reflector.get('path', controllerClass).replace(/\/?$/, '/');
    const baseUrl = this.configService.get<string>('BASE_URL');
    const port = this.configService.get<string>('PORT');
    const uploadFolder = this.configService
      .get<string>('UPLOAD_FOLDER')
      .replace(/\/?$/, '');
    const url = baseUrl.replace('$PORT', port).replace(/\/?$/, '/');

    return next.handle().pipe(
      map((rawData) => {
        if (rawData.data) {
          rawData.data = rawData.data.map((item) => ({
            ...item,
            imagesFull: getImageToDb(
              item.images,
              `${url}${uploadFolder}${prefix}`,
            ),
          }));
        } else {
          rawData.imagesFull = getImageToDb(
            rawData.images,
            `${url}${uploadFolder}${prefix}`,
          );
        }
        return rawData;
      }),
    );
  }
}
