import { NestFactory, Reflector } from '@nestjs/core';
import { AppModule } from './modules/app.module';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import 'dotenv/config';
import { Logger, ValidationPipe } from '@nestjs/common';
import { json, urlencoded } from 'express';
import { TransformationInterceptor } from './interceptors/transform.interceptor';
import { NestExpressApplication } from '@nestjs/platform-express';
import { join } from 'path';
// import { SerializerInterceptor } from './interceptors/serialize.interceptor';

const PORT = process.env.PORT || 3001;
const logger = new Logger('Admin Api Wstore Admin');

async function bootstrap() {
  const app = await NestFactory.create<NestExpressApplication>(AppModule);

  app.setBaseViewsDir(join(__dirname, '..', './src/views'));

  // Cấu hình template engine là Handlebars
  app.setViewEngine('hbs');

  app.use((req, res, next) => {
    const { method, url } = req;
    const start = Date.now();

    res.on('finish', () => {
      const duration = Date.now() - start;
      Logger.log(`${method} ${url} ${res.statusCode} - ${duration}ms`, 'HTTP');
    });

    next();
  });

  app.use(json({ limit: '50mb' }));
  app.use(urlencoded({ extended: true, limit: '50mb' }));
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      // transformOptions: {
      //   enableImplicitConversion: true, // allow conversion underneath
      // },
    }),
  );
  app.enableCors();
  app.useGlobalInterceptors(new TransformationInterceptor(app.get(Reflector)));
  // app.useGlobalInterceptors(new SerializerInterceptor(app.get(Reflector)));

  // Config Swagger module
  const config = new DocumentBuilder()
    .setTitle('Wstore Admin Api')
    .setDescription('Wstore Admin API description')
    .setVersion('1.0')
    .addBearerAuth(
      {
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'JWT',
        name: 'Authorization',
        description: 'Enter JWT token',
        in: 'header',
      },
      'JWT-auth',
    )
    .addSecurityRequirements('JWT-auth')
    .build();

  if (process.env.NODE_ENV === 'sta') {
    app.setGlobalPrefix('wstore-be-sta/v1');
  }

  if (process.env.NODE_ENV === 'pro') {
    app.setGlobalPrefix('wstore-be-pro/v1');
  }

  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('docs', app, document, {
    useGlobalPrefix: true,
  });
  await app.listen(PORT);

  logger.log(`http://localhost:${PORT}/docs`);
}
bootstrap();
