export const recursionCategories = (categories: any, parentId = null): any => {
  // 1. Find Parrent

  const parentCategories = categories.filter(
    (category) => category.parentId === parentId,
  );

  if (parentCategories.length) {
    for (const category of parentCategories) {
      const childs = recursionCategories(categories, category.id);
      category.childs = childs;
    }
  }

  return parentCategories;
};
