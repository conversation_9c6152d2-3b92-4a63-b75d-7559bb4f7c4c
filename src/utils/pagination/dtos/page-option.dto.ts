import { ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsEnum, IsInt, IsOptional, <PERSON>, Min } from 'class-validator';
import { OrderBy } from '../../enums';

export class PageOptionsDto {
  @ApiPropertyOptional({ minimum: 1, default: 1 })
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @IsOptional()
  readonly page?: number = 1;

  @ApiPropertyOptional({ minimum: 1, maximum: 1000, default: 10 })
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @Max(1000)
  @IsOptional()
  readonly limit?: number = 10;

  @ApiPropertyOptional({ enum: OrderBy, default: OrderBy.desc })
  @IsEnum(OrderBy)
  @IsOptional()
  readonly order?: OrderBy = OrderBy.desc;

  get skip(): number {
    return (this.page - 1) * this.limit;
  }
}
