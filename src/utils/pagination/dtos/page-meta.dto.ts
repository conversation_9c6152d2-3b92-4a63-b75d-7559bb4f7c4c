import { ApiProperty } from '@nestjs/swagger';
import { IPageMetaParameters } from '../interfaces/page-meta-parameter.interface';

export class PageMetaDTO {
  @ApiProperty()
  readonly page?: number;

  @ApiProperty()
  readonly limit?: number;

  @ApiProperty()
  readonly totalRows?: number;

  @ApiProperty()
  readonly hasPreviousPage?: boolean;

  @ApiProperty()
  readonly hasNextPage?: boolean;

  @ApiProperty()
  readonly numberOfPage?: number;

  constructor({ pageOptionsDTO, totalRows }: IPageMetaParameters) {
    this.page = pageOptionsDTO.page;
    this.limit = pageOptionsDTO.limit;
    this.totalRows = totalRows;
    this.numberOfPage = Math.ceil(this.totalRows / this.limit);
    this.hasNextPage = this.page > 1;
    this.hasNextPage = this.page < this.numberOfPage;
  }
}
