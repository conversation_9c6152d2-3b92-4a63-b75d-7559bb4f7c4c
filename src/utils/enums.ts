// String enum
export enum RouteName {
  healthz = 'healthz',
  categories = 'categories',
  products = 'products',
  blogs = 'blogs',
  orders = 'orders',
  settings = 'settings',
  attributes = 'attributes',
  properties = 'properties',
  enums = 'enums',
  customers = 'customers',
  locations = 'locations',
  shipFees = 'ship-fees',
  users = 'users',
  roles = 'roles',
  auths = 'auths',
  files = 'files',
  bannerSections = 'banner-sections',
  bannerImages = 'banner-images',
  applications = 'applications',
  socialMedias = 'social-medias',
  permissions = 'permissions',
  rolePermissions = 'role-permissions',
}

export enum CommonStatus {
  active = 'active',
  inActive = 'inactive',
}

export enum OrderBy {
  asc = 'asc',
  desc = 'desc',
}

export enum ProductType {
  single = 'single',
  parent = 'parent',
  child = 'child',
  combo = 'combo',
}

export enum OrderStatus {
  new = 'new',
  received = 'received',
  complete = 'complete',
  cancelled = 'cancelled',
}

export enum ShipType {
  cod = 'cod',
  ispu = 'ispu',
}

export enum CustomerGender {
  male = 'male',
  female = 'female',
  unknown = 'unknown',
}

export enum CategoryType {
  products = 'products',
  news = 'news',
}

export enum LocationVNType {
  countries = 'countries',
  cities = 'cities',
  districts = 'districts',
  wards = 'wards',
}

export enum LocationGlobalType {
  countries = 'countries',
  states = 'states',
  cities = 'cities',
}

export enum ShipCondition {
  amountTotalOver = 'amountTotalOver',
}

export enum FilePage {
  settings = 'settings',
  categories = 'categories',
}

export enum CommonEvent {
  settingCreated = 'setting.created',
  categoryCreated = 'category.created',
}

export enum BannerSectionType {
  sectionBanner = 'sectionBanner',
  sectionUnderBanner = 'sectionUnderBanner',
  sectionBannerProduct = 'sectionBannerProduct',
  sectionPopup = 'sectionPopup',
  sectionMiddle = 'sectionMiddle',
  // sectionInstagram = 'sectionInstagram',
}

export enum SettingImage {
  icon = 'icon',
  verified = 'verified',
}

export enum BlogType {
  news = 'news',
  policies = 'policies',
  reviews = 'reviews',
  instructs = 'instructs',
  aboutMe = 'aboutMe',
}

export enum Mode {
  new = 'new',
  update = 'update',
  delete = 'delete',
}

export enum SortProduct {
  SORT_HOME = 'sortHome',
  SORT_NEW = 'sortNew',
  SORT_BEST_SALE = 'sortBestSale',
  SORT_DISCOUNT = 'sortDiscount',
  SORT_HOT_BODY = 'sortHotBody',
}

export enum ApplicationType {
  instagram = 'instagram',
}
export enum ApplicationGrantType {
  authorizationCode = 'authorization_code',
}
// Number enum must require as const in the end

export const ValidatorFields = {
  strMaxLength: 255,
  strMinLength: 0,
} as const;

export type ValidatorFieldsType = keyof typeof ValidatorFields;

// Declare in export sync to databse
export default {
  CategoryType,
  CommonStatus,
  OrderBy,
  ProductType,
  OrderStatus,
  CustomerGender,
  ShipCondition,
  BlogType,
  ShipType,
  ApplicationType,
  ApplicationGrantType,
};
