import * as bcrypt from 'bcrypt';
import { randomBytes } from './crypto.util';

const SALT_ROUND = Number(process.env.PW_SALT_ROUND) || 10;

export const comparePassword = (
  password: string,
  hashPassword: string,
): boolean => bcrypt.compareSync(password, hashPassword);

export const generateHashPassword = (
  password: string,
  saltRounds?: number,
): string => bcrypt.hashSync(password, saltRounds || SALT_ROUND);

export const generateRefreshToken = (): string => randomBytes();
