export const configAggregateCountAll = (limit: number, page: number) => {
  return [
    {
      $set: {
        id: '$_id',
      },
    },
    {
      $facet: {
        rows: [{ $skip: (page - 1) * limit }, { $limit: limit }],
        totalCount: [{ $count: 'count' }],
      },
    },
    {
      $addFields: {
        totalRows: {
          $ifNull: [{ $arrayElemAt: ['$totalCount.count', 0] }, 0],
        },
        numberOfPage: {
          $ceil: {
            $divide: [{ $arrayElemAt: ['$totalCount.count', 0] }, limit],
          },
        },
      },
    },
  ];
};
