export const persitImageToDB = (data: string | string[] | null) => {
  let images = data || {};
  if (Array.isArray(data)) {
    images = data.reduce((result, current) => {
      if (current) {
        result[current] = current;
        return result;
      }
    }, {});
  } else if (typeof data === 'string') {
    images = data
      ? {
          [data.toString()]: data.toString(),
        }
      : {};
  }
  return images;
};

export const getImageToDb = (images, baseUrl) => {
  if (!images) return [];

  if (typeof images === 'object' && !Array.isArray(images)) {
    return Object.keys(images).reduce((result, key) => {
      if (images[key].image) {
        result[key] = {
          ...images[key],
          ...(images[key].image ? { image: baseUrl + images[key].image } : {}),
        };
      } else {
        result[key] = baseUrl + images[key];
      }

      return result;
    }, {});
  } else if (Array.isArray(images)) {
    return images.map((image) => baseUrl + image);
  }
  return baseUrl + images;
};
