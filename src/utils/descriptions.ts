import { RouteName } from './enums';

export const description = {
  permissions: {
    name: 'Permissions',
    validator: {
      id: 'id quyền',
      permissionName: 'Tên quyền',
      description: '<PERSON><PERSON> tả quyền',
    },
  },
  applications: {
    name: 'Applications',
    validator: {
      id: 'id ứng dụng',
      name: 'Tên ứng dụng',
      type: 'Kiểu ứng dụng',
      clientId: 'clientId',
      clientSecret: 'clientSecret',
      grantType: 'grantType',
      authUrl: 'authUrl',
      tokenUrl: 'tokenUrl',
      responseType: 'responseType',
      redirectUrl: 'redirectUrl',
      scope: 'scope',
      applicationType: 'applicationType',
      active: 'active',
    },
  },
  bannerImages: {
    name: 'Banner Images',
    validator: {
      id: 'id banner',
      name: 'Tên banner',
      code: 'Mã banner',
      type: 'Kiểu banner',
      max: 'S<PERSON> lượng tối đa',
      subHeading: 'Tiêu đề 1',
      subHeading2: 'Tiêu đề 2',
      subHeading3: 'Tiêu đề 3',
      markupText: 'Ch<PERSON> Markup',
    },
  },
  bannerSections: {
    name: 'Banner Sections',
    validator: {
      id: 'id banner',
      name: 'Tên banner',
      code: 'Mã banner',
      type: 'Kiểu banner',
      max: 'Số lượng tối đa',
      url: 'Banner Url mặc định',
    },
  },
  files: {
    name: 'File',
    validator: {
      id: 'id thư mục',
      page: 'Page',
      name: 'Tên thư mục',
      referenceId: 'Mã tham chiếu',
      url: 'Đường dẫn thư mục',
      position: 'Vị trí thư mục',
    },
  },
  auths: {
    name: 'auth',
    validator: {
      username: 'Tên đăng nhập',
      password: 'Mật khẩu',
    },
  },
  enums: {
    name: 'enum',
    validator: {
      id: 'id enum',
      key: 'Khoá enum',
      name: 'Tên enum',
      value: 'Giá trị enum',
    },
  },
  roles: {
    name: 'Vai trò',
    validator: {
      id: 'id vai trò',
      roleName: 'tên vai trò',
    },
  },
  users: {
    name: 'Tài khoản',
    validator: {
      id: 'id tài khoản',
      name: 'Tên tài khoản',
      password: 'Mật khẩu tài khoản',
      roleId: 'id vai trò',
      firstName: 'Tên tài khoản',
      lastName: 'Họ tài khoản',
      userEmail: 'Email tài khoản',
      userPhone: 'SĐT tài khoản',
      userAddress: 'Địa chỉ tài khoản',
    },
  },
  setting: {
    name: 'cấu hình',
    validator: {
      id: 'id cấu hình ',
      companyName: 'Tên công ty',
      companyAddress: 'Địa chỉ công ty',
      companyEmail: 'Email công ty',
      companyPhone: 'SĐT công ty',
      companyWebsite: 'Website công ty',
      companyTaxNo: 'MST công ty',
      companyTaxNoDate: 'Ngày cấp',
      companyTaxLink: 'Đường dẫn thuế',
      facebook: 'Link facebook',
      instagram: 'Link instagram',
      youtube: 'Link youtube',
      tiktok: 'Link tiktok',
      image: 'Hình ảnh',
      imageType: 'Kiểu hình ảnh',
      verifiedImageLink: 'Đường dẫn đăng ký bộ công thương',
    },
  },
  shipFee: {
    name: 'phí ship',
    validator: {
      id: 'Mã ship fee',
      name: 'Nhập tên khu vực',
      regionId: 'Mã vùng',
      regionName: 'Tên vùng',
      regionType: 'Loại vùng',
      regionOptionId: 'Loại thanh toán',
      condition: 'Điều kiện',
    },
  },
  locations: {
    name: 'tỉnh thành, quận huyện Việt Nam',
    validator: {
      type: 'Loại cities, districts, wards',
    },
  },
  countries: {
    name: 'quốc gia',
    validator: {
      code: 'Mã quốc gia',
      name: 'Tên quốc gia',
    },
  },
  states: {
    name: 'tỉnh / thành phố',
    validator: {
      code: 'Mã tỉnh/thành phố',
      name: 'Tên tỉnh/thành phố',
    },
  },
  cities: {
    name: 'thành phố / tỉnh',
    validator: {
      code: 'Mã thành phố/tỉnh',
      name: 'Tên thành phố',
    },
  },
  dictricts: {
    name: 'quận / huyện',
    validator: {
      code: 'Mã quận/huyện',
      name: 'Tên quận/huyện',
    },
  },
  wards: {
    name: 'phường / xã',
    validator: {
      code: 'Mã phường/xã',
      name: 'Tên phường/xã',
    },
  },
  customers: {
    name: 'khách hàng',
    validator: {
      id: 'Mã khách hàng',
      name: 'Tên khách hàng',
      phone: 'Số điện thoại khách hàng',
      address: 'Địa chỉ khách hàng',
      gender: 'Giới tính',
    },
  },
  orders: {
    name: 'đơn hàng',
    validator: {
      id: 'id đơn hàng',
      receiverName: 'Tên người nhận hàng',
      receiverPhone: 'SĐT người nhận hàng',
      receiverAddress: 'Địa chỉ người nhận hàng',
      totalAmount: 'Tổng tiền đơn hàng',
      totalDiscount: 'Tổng tiền vận chuyển',
      shipAmount: 'Phí vận chuyển',
      shipType: 'Loại hình vận chuyển',
      status: 'Trạng thái đơn hàng',
    },
  },
  orderItems: {
    validator: {
      quantity: 'Số lượng',
      currentPrice: 'Số tiền hiện tại',
      discount: 'Giảm giá',
      totalAmount: 'Tổng giá tiền sản phẩm',
    },
  },
  categories: {
    name: 'danh mục',
    validator: {
      id: 'id danh mục',
      name: 'Tên danh mục',
      parent: 'Danh mục cha',
      type: 'Loại danh mục',
    },
  },
  products: {
    name: 'sản phẩm',
    validator: {
      id: 'Mã sản phẩm',
      ids: 'Nhiều mã sản phẩm',
      name: 'Tên sản phẩm',
      code: 'Mã sản phẩm',
      parent: 'Sản phẩm cha',
      type: 'Loại sản phẩm',
      sellingPrice: 'Giá bán lẻ',
      listedPrice: 'Giá bán ban đầu',
      description: 'Nội dung',
      isNew: 'Mới',
      isBestSale: 'Bán chạy',
      isDiscount: 'Giảm giá',
      childMode: 'Trạng thái update/create sản phẩm con',
      imageSize: 'Hình ảnh chọn size',
    },
  },
  attributes: {
    name: 'thuộc tính',
    validator: {
      id: 'Mã thuộc tính',
    },
  },
  properties: {
    name: 'tính chất',
    validator: {
      id: 'id tính chất',
      name: 'tên tính chất',
      color: 'màu sắc',
      isNew: 'cập nhật | tạo mới',
    },
  },
  settings: {
    name: 'cài đặt',
  },
  blogs: {
    name: 'tin tức',
    validator: {
      id: 'id bài viết',
      title: 'Tiêu đề',
      type: 'Loại bài viết',
      description: 'Mô tả bài viết',
      sortDescription: 'Mô tả ngắn bài viết',
    },
  },
  healthz: {
    name: 'heart Beat',
  },
  common: {
    status: 'Trạng thái',
    images: 'Hình ảnh',
    metaTitle: 'Thẻ metaTitle',
    metaDescription: 'Thẻ metaDescription',
    metaKeyword: 'Thẻ metaKeyword',
    slug: 'slug',
    createdAt: 'Ngày tạo',
    tab: 'mục',
  },
  httpStatus: {
    ok: 'Successfully',
    forbidden: 'Forbidden.',
    unAuthorized: 'Unauthorized.',
    badRequest: 'Unauthorized.',
    interalServer: 'Internal Server Exception.',
    notFound: 'Not Found.',
  },
  controller: {
    gets: 'Lấy danh sách',
    getOne: 'Lấy chi tiết',
    post: 'Tạo mới',
    put: 'Cập nhật',
    delete: 'Xoá',
    clean: 'Quét dọn',
    getsDescription: 'Tính năng này được sử dụng để lấy toàn bộ',
    getOneDescription: 'Tính năng này được sử dụng để lấy chi tiết',
    postDescription: 'Tính năng này được sử dụng để tạo',
    putDescription: 'Tính năng này được sử dụng để cập nhật',
    deleteDescription: 'Tính năng này được sử dụng để xoá',
  },
};

export const getDescription = (RouteName: RouteName, method: string) => {
  const masterData = description[RouteName] || {
    name: 'chưa xác định',
  };
  return `${method} ${masterData.name}`;
};
