import * as fs from 'fs';
import { mkdirp } from 'mkdirp';

export const getFileExtension = (filename: string): string =>
  filename.split('.').pop() as string;

export const deleteFile = (path: string): void | undefined =>
  fs.unlinkSync(path);

export const createDirectory = (path: string): string | void =>
  mkdirp.sync(path);

export const readFileAsync = (path: string): Promise<Buffer> =>
  new Promise((resolve, reject) => {
    fs.readFile(
      path,
      (error: NodeJS.ErrnoException | null, content: Buffer) => {
        if (error) {
          reject(error);
        }
        resolve(content);
      },
    );
  });

export const readdirAsync = (
  path: string,
  options?: fs.ObjectEncodingOptions & {
    withFileTypes: true;
    recursive?: boolean;
  },
): Promise<fs.Dirent[]> =>
  new Promise((resolve, reject) => {
    fs.readdir(
      path,
      options,
      (error: NodeJS.ErrnoException | null, files: fs.Dirent[]) => {
        if (error) {
          reject(error);
        }
        resolve(files);
      },
    );
  });
