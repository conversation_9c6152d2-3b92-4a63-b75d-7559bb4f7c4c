import { Types } from 'mongoose';

export const toObjectId = (string: string | Types.ObjectId) => {
  return string ? new Types.ObjectId(string) : null;
};

export const newObjectId = () => {
  const timestamp = Math.floor(new Date().getTime() / 1000).toString(16);

  const objectId =
    timestamp +
    'xxxxxxxxxxxxxxxx'
      .replace(/[x]/g, () => {
        return Math.floor(Math.random() * 16).toString(16);
      })
      .toLowerCase();

  return objectId;
};
