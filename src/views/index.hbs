<html lang='en'>

<head>
  <meta charset='UTF-8' />
  <meta name='viewport' content='width=device-width, initial-scale=1.0' />
  <meta http-equiv='X-UA-Compatible' content='ie=edge' />
  <title>Authentication</title>
  <style>
    body {
      width: 100%;
      height: 100%;
      text-align: center;
      padding-top: 100px;
      font-family: sans-serif;
      font-weight: 400;
    }

    .text-authenticate {
      font-weight: 800;
      margin-top: 10px;
    }

    .text-message {
      margin-top: 10px;
    }
  </style>
</head>

<body>
  <svg xmlns='http://www.w3.org/2000/svg' width='100' height='100' viewBox='0 0 100 100' fill='none'>
    <circle cx='50' cy='50' r='50' fill='#26B47F'></circle>
    <path
      d='M43.875 5.37327C43.875 2.54484 41.6154 0.25 38.8258 0.25C37.2459 0.25 35.837 0.987934 34.9103 2.14009L34.9084 2.13816L16.7115 24.8158L8.88268 16.2132L8.87889 16.2171C7.95606 15.2036 6.64012 14.5659 5.17421 14.5659C2.38477 14.5639 0.125 16.8567 0.125 19.6872C0.125 21.0282 0.637698 22.2439 1.46749 23.1572L1.46369 23.161L13.2421 36.1026L13.2459 36.0988C14.1687 37.1122 15.4847 37.75 16.9506 37.75C18.5304 37.75 19.9394 37.0121 20.866 35.8599L20.8679 35.8618L42.7428 8.60646L42.7409 8.60453C43.4492 7.724 43.8745 6.59882 43.8745 5.37342L43.875 5.37327Z'
      fill='white' transform='translate(28, 32)'></path>
  </svg>

  <div class='text-authenticate'>Authenticated Successful</div>

  <div class='text-message'>This window will close automatically in <span id="countdown"></span> seconds.</div>

  <script>
    var closeAfterSeconds = 5; // Set the number of seconds before closing
    var countdownElement = document.getElementById("countdown");

    var countdown = closeAfterSeconds;
    var interval = setInterval(() => {
      countdown--;
      countdownElement.textContent = countdown;
      if (countdown <= 0) {
        clearInterval(interval);
        window.close();
      }
    }, 1000);
  </script>
</body>

</html>