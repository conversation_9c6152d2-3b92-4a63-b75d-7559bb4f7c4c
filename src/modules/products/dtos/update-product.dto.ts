import { toObjectId } from './../../../utils/object-id';
import { ProductType } from '../../../utils/enums';
import { CreateProductDto } from './create-product.dto';
import { Types } from 'mongoose';
import { ApiProperty } from '@nestjs/swagger';
import { IsMongoId, ValidateNested } from 'class-validator';
import { description } from '../../../utils/descriptions';
import { keyBy } from 'lodash';
import { UpdateChildDto } from './update-child.dto';
import { formatStringWithDash } from '../../../utils/string';
import { IsChildsValid } from '../../../decorators/validators/is-child-valid';
import { Type } from 'class-transformer';

export class UpdateProductDto extends CreateProductDto {
  @ApiProperty({
    type: 'string',
    required: false,
    description: description.products.validator.id,
  })
  @IsMongoId()
  id: Types.ObjectId;

  @ApiProperty({ type: UpdateChildDto, isArray: true })
  @ValidateNested({ each: true })
  @IsChildsValid({
    message: "Childs array must be empty when productType is 'single'.",
  })
  @Type(() => UpdateChildDto)
  childs: UpdateChildDto[];

  constructor(data: Partial<UpdateProductDto>) {
    super(data);
    Object.assign(this, data);
  }

  public static fromProduct(payload: UpdateProductDto): UpdateProductDto {
    const productType = payload.parentId
      ? ProductType.child
      : payload.productType;

    return new UpdateProductDto({
      id: toObjectId(payload.id),
      productName: payload.productName,
      productCode: payload.productCode,
      productType: productType,
      parentId: toObjectId(payload.parentId),
      categoryId: toObjectId(payload.categoryId),
      sellingPrice: payload.sellingPrice,
      listedPrice: payload.listedPrice,
      status: payload.status,
      attributes: keyBy(payload.attributes, 'id'),
      description: payload.description,
      slug: payload.slug,
      metaTitle: payload.metaTitle,
      metaDescription: payload.metaDescription,
      metaKeyword: payload.metaKeyword,
      isNew: payload.isNew,
      isBestSale: payload.isBestSale,
      isDiscount: payload.isDiscount,
      images: payload.images,
      weight: payload.weight,
      dimensions: payload.dimensions,
      imageSize: payload.imageSize,
    });
  }

  public static fromProductChild(
    payload: UpdateProductDto,
    parentIdFromDb: Types.ObjectId,
  ): UpdateChildDto[] {
    return (payload.childs || []).map((child: UpdateChildDto) => ({
      ...child,
      parentId: toObjectId(parentIdFromDb),
      categoryId: toObjectId(payload.categoryId),
      productType: ProductType.child,
      sellingPrice: child.sellingPrice,
      listedPrice: payload.listedPrice,
      slug: formatStringWithDash(child.productName),
      attributes: child.attributes,
      weight: payload.weight,
      dimensions: payload.dimensions,
      mode: child.mode,
    }));
  }
}
