import { ApiProperty } from '@nestjs/swagger';
import {
  IsBoolean,
  IsMongoId,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  Length,
  MaxLength,
  <PERSON><PERSON>ength,
  ValidateIf,
  ValidateNested,
} from 'class-validator';
import { description } from '../../../utils/descriptions';
import {
  CommonStatus,
  ProductType,
  ValidatorFields,
} from '../../../utils/enums';
import { CreateChildDto } from './create-child.dto';
import { IsChildsValid } from '../../../decorators/validators/is-child-valid';
import { AttributeDto } from './attribute.dto';
import { IsParentIdValid } from '../../../decorators/validators/is-parent-valid';
import { Types } from 'mongoose';
import { toObjectId } from '../../../utils/object-id';
import { Type } from 'class-transformer';
import { keyBy } from 'lodash';
import { ImageDto } from './image.dto';
import { formatStringWithDash } from '../../../utils/string';
export class CreateProductDto {
  @ApiProperty({
    type: 'string',
    required: true,
    description: description.products.validator.name,
  })
  @IsNotEmpty()
  @IsString()
  @Length(ValidatorFields.strMinLength, ValidatorFields.strMaxLength)
  productName: string;

  @ApiProperty({
    type: 'string',
    required: true,
    description: description.products.validator.code,
  })
  @IsNotEmpty()
  @IsString()
  @MaxLength(ValidatorFields.strMaxLength)
  @MinLength(ValidatorFields.strMinLength)
  productCode: string;

  @ApiProperty({
    enum: [ProductType.parent, ProductType.single, ProductType.combo],
    default: ProductType.single,
    required: true,
    description: description.products.validator.type,
  })
  @IsString()
  productType: string;

  @ApiProperty({
    type: 'string',
    required: false,
    description: description.products.validator.parent,
  })
  @ValidateIf((dto) => dto.parentId)
  @IsMongoId()
  @IsParentIdValid({
    message: "parentId must be null when productType is 'parent'.",
  })
  parentId: Types.ObjectId;

  @ApiProperty({
    type: 'string',
    required: false,
    description: description.categories.validator.id,
  })
  @ValidateIf((dto) => dto.categoryId)
  @IsMongoId()
  categoryId: Types.ObjectId;

  @ApiProperty({
    type: 'number',
    required: false,
    default: 0,
    description: description.products.validator.sellingPrice,
  })
  @IsNumber()
  sellingPrice: number;

  @ApiProperty({
    type: 'number',
    required: false,
    default: 0,
    description: description.products.validator.listedPrice,
  })
  @IsNumber()
  listedPrice: number;

  @ApiProperty({
    enum: Object.values(CommonStatus),
    default: CommonStatus.active,
    description: description.common.status,
    required: true,
  })
  @IsString()
  status: string;

  @ApiProperty({
    description: 'A key-value pair object where the values are objects',
    type: AttributeDto,
    isArray: true,
  })
  @ValidateNested({ each: true })
  @Type(() => AttributeDto)
  @ValidateIf((field) => field.attributes && field.attributes.length)
  attributes: Record<string, AttributeDto>;

  @ApiProperty({ type: CreateChildDto, isArray: true })
  @ValidateNested({ each: true })
  @IsChildsValid({
    message: "Childs array must be empty when productType is 'single'.",
  })
  @Type(() => CreateChildDto)
  childs: CreateChildDto[];

  @ApiProperty({
    type: 'string',
    required: true,
    description: description.products.validator.description,
  })
  @IsString()
  @ValidateIf((field) => field.description)
  description: string;

  @ApiProperty({
    type: 'string',
    required: true,
    description: description.common.slug,
  })
  @IsNotEmpty()
  @IsString()
  slug: string;

  @ApiProperty({
    type: 'string',
    required: true,
    description: description.common.metaTitle,
  })
  @IsString()
  metaTitle: string;

  @ApiProperty({
    type: 'string',
    required: true,
    description: description.common.metaDescription,
  })
  @IsString()
  metaDescription: string;

  @ApiProperty({
    type: 'string',
    required: true,
    description: description.common.metaKeyword,
  })
  @IsString()
  metaKeyword: string;

  @ApiProperty({
    type: 'boolean',
    required: true,
    description: description.products.validator.isNew,
  })
  @IsBoolean()
  isNew: boolean;

  @ApiProperty({
    type: 'boolean',
    required: true,
    description: description.products.validator.isBestSale,
  })
  @IsBoolean()
  isBestSale: boolean;

  @ApiProperty({
    type: 'boolean',
    required: true,
    description: description.products.validator.isDiscount,
  })
  @IsBoolean()
  isDiscount: boolean;

  @ApiProperty({ type: ImageDto, isArray: true })
  @ValidateNested({ each: true })
  @Type(() => ImageDto)
  images: ImageDto[];

  @ApiProperty({
    type: 'string',
    required: true,
    description: description.products.validator.name,
  })
  @IsString()
  @IsOptional()
  @Length(ValidatorFields.strMinLength, ValidatorFields.strMaxLength)
  weight: string;

  @ApiProperty({
    type: 'string',
    required: true,
    description: description.products.validator.name,
  })
  @IsString()
  @IsOptional()
  @Length(ValidatorFields.strMinLength, ValidatorFields.strMaxLength)
  dimensions: string;

  @ApiProperty({
    type: 'string',
    required: true,
    description: description.products.validator.imageSize,
  })
  @IsString()
  @IsOptional()
  imageSize: string;

  constructor(data: Partial<CreateProductDto>) {
    Object.assign(this, data);
  }

  public static fromProduct(payload: CreateProductDto): CreateProductDto {
    const productType = payload.parentId
      ? ProductType.child
      : payload.productType;

    return new CreateProductDto({
      productName: payload.productName,
      productCode: payload.productCode,
      productType: productType,
      parentId: toObjectId(payload.parentId),
      categoryId: toObjectId(payload.categoryId),
      sellingPrice: payload.sellingPrice,
      listedPrice: payload.listedPrice,
      status: payload.status,
      attributes: keyBy(payload.attributes, 'id'),
      description: payload.description,
      slug: payload.slug,
      metaTitle: payload.metaTitle,
      metaDescription: payload.metaDescription,
      metaKeyword: payload.metaKeyword,
      isNew: payload.isNew,
      isBestSale: payload.isBestSale,
      isDiscount: payload.isDiscount,
      images: payload.images,
      weight: payload.weight,
      dimensions: payload.dimensions,
      imageSize: payload.imageSize,
    });
  }

  public static fromProductChild(
    payload: CreateProductDto,
    parentIdFromDb: Types.ObjectId,
  ): CreateChildDto[] {
    return (payload.childs || []).map((child: CreateChildDto) => ({
      ...child,
      parentId: toObjectId(parentIdFromDb),
      variants: toObjectId(parentIdFromDb),
      categoryId: toObjectId(payload.categoryId),
      productType: ProductType.child,
      sellingPrice: payload.sellingPrice,
      listedPrice: payload.listedPrice,
      slug: formatStringWithDash(child.productName),
      attributes: child.attributes,
      metaTitle: payload.metaTitle,
      metaDescription: payload.metaDescription,
      metaKeyword: payload.metaKeyword,
      isNew: payload.isNew,
      isBestSale: payload.isBestSale,
      isDiscount: payload.isDiscount,
      weight: payload.weight,
      dimensions: payload.dimensions,
    }));
  }
  // validate async parentId and categoryId
}
