import { ApiProperty } from '@nestjs/swagger';
import { PropertyDto } from './property.dto';
import { IsObject, IsString } from 'class-validator';
import { description } from '../../../utils/descriptions';

export class AttributeDto {
  @ApiProperty({
    type: 'string',
    required: true,
    description: description.attributes.validator.id,
  })
  @IsString()
  id: string;

  @ApiProperty({ type: PropertyDto })
  @IsObject()
  properties: Record<string, PropertyDto>;
}
