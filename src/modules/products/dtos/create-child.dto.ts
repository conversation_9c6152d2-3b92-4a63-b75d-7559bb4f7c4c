import { ApiProperty } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsObject,
  IsString,
  MaxLength,
  <PERSON><PERSON>ength,
  ValidateNested,
} from 'class-validator';
import { CommonStatus, ValidatorFields } from '../../../utils/enums';
import { description } from '../../../utils/descriptions';
import { Types } from 'mongoose';
import { AttributeDto } from './attribute.dto';
import { ImageDto } from './image.dto';
import { Type } from 'class-transformer';

export class CreateChildDto {
  @ApiProperty({
    type: 'string',
    required: true,
    description: description.products.validator.name,
  })
  @IsNotEmpty()
  @IsString()
  @MaxLength(ValidatorFields.strMaxLength)
  @MinLength(ValidatorFields.strMinLength)
  productName: string;

  @ApiProperty({
    type: 'string',
    required: true,
    description: description.products.validator.code,
  })
  @IsNotEmpty()
  @IsString()
  @MaxLength(ValidatorFields.strMaxLength)
  @MinLength(ValidatorFields.strMinLength)
  productCode: string;

  @ApiProperty({
    enum: Object.values(CommonStatus),
    default: CommonStatus.active,
    description: description.common.status,
    required: true,
  })
  @IsString()
  status: string;

  @ApiProperty({
    type: 'string',
    required: true,
    description: description.common.slug,
  })
  @IsNotEmpty()
  @IsString()
  slug: string;

  @ApiProperty({
    description: 'A key-value pair object where the values are objects',
    type: AttributeDto,
  })
  @IsObject()
  attributes: Record<string, AttributeDto>;

  categoryId: Types.ObjectId;

  @ApiProperty({ type: ImageDto, isArray: true })
  @ValidateNested({ each: true })
  @Type(() => ImageDto)
  images: ImageDto[];
}
