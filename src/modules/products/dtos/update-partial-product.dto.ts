import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsMongoId, IsOptional } from 'class-validator';
import { description } from '../../../utils/descriptions';
import { Types } from 'mongoose';
import { toObjectId } from '../../../utils/object-id';

export class UpdatePartialProductDto {
  @ApiProperty({
    type: 'string',
    required: false,
    description: description.products.validator.id,
  })
  @IsMongoId()
  id: Types.ObjectId;

  @ApiProperty({
    type: 'boolean',
    required: true,
    description: description.products.validator.isNew,
  })
  @IsBoolean()
  @IsOptional()
  isNew: boolean;

  @ApiProperty({
    type: 'boolean',
    required: true,
    description: description.products.validator.isBestSale,
  })
  @IsBoolean()
  @IsOptional()
  isBestSale: boolean;

  @ApiProperty({
    type: 'boolean',
    required: true,
    description: description.products.validator.isDiscount,
  })
  @IsBoolean()
  @IsOptional()
  isDiscount: boolean;

  constructor(data: Partial<UpdatePartialProductDto>) {
    Object.assign(this, data);
  }

  public static fromProduct(
    payload: UpdatePartialProductDto,
  ): UpdatePartialProductDto {
    return new UpdatePartialProductDto({
      id: toObjectId(payload.id),
      isNew: payload.isNew,
      isBestSale: payload.isBestSale,
      isDiscount: payload.isDiscount,
    });
  }
}
