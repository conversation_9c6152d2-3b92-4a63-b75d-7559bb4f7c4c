import { ApiProperty } from '@nestjs/swagger';
import {
  ArrayMinSize,
  IsArray,
  IsEnum,
  IsOptional,
  IsString,
} from 'class-validator';
import { description } from '../../../utils/descriptions';
import { SortProduct } from '../../../utils/enums';

export class ReorderProductDto {
  @ApiProperty({
    type: String,
    isArray: true,
    required: false,
    description: description.products.validator.ids,
  })
  @IsArray()
  @ArrayMinSize(1)
  @IsString({ each: true })
  @IsOptional()
  ids: string[];

  @ApiProperty({
    enum: SortProduct,
    required: true,
    description: description.common.tab,
  })
  @IsEnum(SortProduct)
  tab: SortProduct;
}
