import { ApiProperty } from '@nestjs/swagger';
import {
  IsMongoId,
  IsNotEmpty,
  IsNumber,
  IsObject,
  IsString,
  MaxLength,
  MinLength,
  ValidateNested,
} from 'class-validator';
import { CommonStatus, ValidatorFields } from '../../../utils/enums';
import { description } from '../../../utils/descriptions';
import { Types } from 'mongoose';
import { AttributeDto } from './attribute.dto';
import { ImageDto } from './image.dto';
import { Type } from 'class-transformer';
import { Optional } from '@nestjs/common';

export class UpdateChildDto {
  @ApiProperty({
    type: 'string',
    required: false,
    description: description.products.validator.id,
  })
  @IsMongoId()
  id: Types.ObjectId;

  @ApiProperty({
    type: 'string',
    required: true,
    description: description.products.validator.name,
  })
  @IsNotEmpty()
  @IsString()
  @MaxLength(ValidatorFields.strMaxLength)
  @MinLength(ValidatorFields.strMinLength)
  productName: string;

  @ApiProperty({
    type: 'string',
    required: true,
    description: description.products.validator.code,
  })
  @IsNotEmpty()
  @IsString()
  @MaxLength(ValidatorFields.strMaxLength)
  @MinLength(ValidatorFields.strMinLength)
  productCode: string;

  @ApiProperty({
    enum: Object.values(CommonStatus),
    default: CommonStatus.active,
    description: description.common.status,
    required: true,
  })
  @IsString()
  status: string;

  @ApiProperty({
    type: 'string',
    required: true,
    description: description.common.slug,
  })
  @IsNotEmpty()
  @IsString()
  slug: string;

  @ApiProperty({
    description: 'A key-value pair object where the values are objects',
    type: AttributeDto,
  })
  @IsObject()
  attributes: Record<string, AttributeDto>;

  categoryId: Types.ObjectId;

  @ApiProperty({ type: ImageDto, isArray: true })
  @ValidateNested({ each: true })
  @Type(() => ImageDto)
  images: ImageDto[];

  @ApiProperty({
    type: 'string',
    required: true,
    description: description.products.validator.childMode,
  })
  @IsString()
  @Optional()
  mode: string;

  @ApiProperty({
    type: 'number',
    required: false,
    default: 0,
    description: description.products.validator.sellingPrice,
  })
  @IsNumber()
  sellingPrice: number;
}
