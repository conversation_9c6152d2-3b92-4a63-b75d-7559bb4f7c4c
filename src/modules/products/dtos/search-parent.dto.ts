import { ApiProperty } from '@nestjs/swagger';
import { PageOptionsDto } from '../../../utils/pagination/dtos/page-option.dto';
import { description } from '../../../utils/descriptions';
import { ProductType } from '../../../utils/enums';
import { IsArray, IsOptional, IsString } from 'class-validator';

export class SearchParentDto extends PageOptionsDto {
  @ApiProperty({
    type: 'string',
    required: false,
    description: description.products.validator.name,
  })
  @IsString()
  @IsOptional()
  productName: string;

  @ApiProperty({
    enum: [ProductType.parent, ProductType.single, ProductType.combo],
    required: false,
    description: description.products.validator.type,
    isArray: true,
  })
  @IsArray()
  @IsOptional()
  productType: string[];

  constructor(data: Partial<SearchParentDto>) {
    super();
    Object.assign(this, data);
  }
}
