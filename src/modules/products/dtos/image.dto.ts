import { ApiProperty } from '@nestjs/swagger';
import { IsString } from 'class-validator';
import { description } from '../../../utils/descriptions';

export class ImageDto {
  @ApiProperty({
    type: 'string',
    required: true,
    description: description.files.validator.id,
  })
  @IsString()
  id: string;

  @ApiProperty({
    type: 'string',
    required: true,
    description: description.files.validator.url,
  })
  @IsString()
  url: string;

  @ApiProperty({
    type: 'string',
    required: true,
    description: description.files.validator.position,
  })
  @IsString()
  position: string;
}
