import { ApiProperty } from '@nestjs/swagger';
import { PageOptionsDto } from '../../../utils/pagination/dtos/page-option.dto';
import { description } from '../../../utils/descriptions';
import { ProductType, SortProduct } from '../../../utils/enums';
import { IsArray, IsEnum, IsOptional, IsString } from 'class-validator';

export class SearchProductDto extends PageOptionsDto {
  @ApiProperty({
    type: 'string',
    required: false,
    description: description.products.validator.name,
  })
  @IsString()
  @IsOptional()
  productName: string;

  @ApiProperty({
    enum: [ProductType.parent, ProductType.single, ProductType.combo],
    required: false,
    description: description.products.validator.type,
    isArray: true,
  })
  @IsArray()
  @IsOptional()
  productType: string[];

  @ApiProperty({
    type: 'string',
    required: false,
    description: description.categories.validator.id,
  })
  @IsString()
  @IsOptional()
  categoryId: string;

  @ApiProperty({
    type: 'string',
    required: false,
    description: description.common.createdAt,
  })
  @IsOptional()
  createdAt: string;

  @ApiProperty({
    enum: SortProduct,
    required: false,
    description: description.common.tab,
  })
  @IsEnum(SortProduct)
  @IsOptional()
  tab: SortProduct;

  constructor(data: Partial<SearchProductDto>) {
    super();
    Object.assign(this, data);
  }
}
