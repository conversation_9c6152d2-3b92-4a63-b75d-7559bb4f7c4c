import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument, Types } from 'mongoose';
import { CommonStatus, ProductType } from '../../../utils/enums';
import { BaseSchema } from '../../shared/base/base.schema';
import { configSchema } from '../../../utils/schema';

export type ProductDocument = HydratedDocument<Product>;

@Schema(configSchema)
export class Product extends BaseSchema<Product> {
  @Prop({
    type: String,
    required: true,
  })
  productName: string;

  @Prop({
    type: String,
    required: true,
  })
  productCode: string;

  @Prop({
    type: String,
    enum: Object.values(ProductType),
    default: ProductType.single,
    required: true,
  })
  productType: string;

  @Prop({
    type: Types.ObjectId,
    default: null,
  })
  parentId: Types.ObjectId;

  @Prop({
    type: String,
    default: null,
  })
  categoryId: string;

  @Prop({
    type: Number,
    default: 0,
    required: true,
  })
  sellingPrice: number;

  @Prop({
    type: Number,
    default: 0,
    required: true,
  })
  listedPrice: number;

  @Prop({
    type: Object,
    default: {},
    required: true,
  })
  attributes: object;

  @Prop({
    type: String,
    enum: Object.values(CommonStatus),
    default: CommonStatus.active,
    required: true,
  })
  status: string;

  @Prop({
    type: String,
    default: '',
    required: false,
  })
  description: string;

  @Prop({
    type: String,
    required: true,
    default: null,
  })
  slug: string;

  @Prop({
    type: String,
    required: false,
    default: '',
  })
  metaTitle: string;

  @Prop({
    type: String,
    required: false,
    default: '',
  })
  metaDescription: string;

  @Prop({
    type: String,
    required: false,
    default: '',
  })
  metaKeyword: string;

  @Prop({
    type: Object,
    required: true,
    default: [],
  })
  images: object;

  @Prop({
    type: Boolean,
    default: false,
    required: true,
  })
  isNew: boolean;

  @Prop({
    type: Boolean,
    default: false,
    required: true,
  })
  isBestSale: boolean;

  @Prop({
    type: Boolean,
    default: false,
    required: true,
  })
  isDiscount: boolean;

  @Prop({
    type: String,
    required: false,
    default: '',
  })
  weight: string;

  @Prop({
    type: String,
    required: false,
    default: '',
  })
  dimensions: string;

  // Add 10-02-2024
  @Prop({
    type: String,
    required: false,
    default: '',
  })
  imageSize: string;

  @Prop({
    type: String,
    required: false,
    default: '',
  })
  sortHome: string;

  @Prop({
    type: String,
    required: false,
    default: '',
  })
  sortNew: string;

  @Prop({
    type: String,
    required: false,
    default: '',
  })
  sortBestSale: string;

  @Prop({
    type: String,
    required: false,
    default: '',
  })
  sortDiscount: string;

  @Prop({
    type: String,
    required: false,
    default: '',
  })
  sortHotBody: string;
}

export const ProductSchema = SchemaFactory.createForClass(Product);

ProductSchema.virtual('variants', {
  ref: 'Product',
  localField: '_id',
  foreignField: 'parentId',
});

ProductSchema.set('toObject', { virtuals: true });
ProductSchema.set('toJSON', { virtuals: true });
