import { Expose, Type } from 'class-transformer';

class ProductImageResponse {
  @Expose()
  id: string;

  @Expose()
  url: string;

  @Expose()
  position: string;
}
export class ProductResponse {
  @Expose()
  id: string;

  @Expose()
  productName: string;

  @Expose()
  productCode: string;

  @Expose()
  productType: string;

  @Expose()
  parentId: string;

  @Expose()
  categoryId: string;

  @Expose()
  sellingPrice: number;

  @Expose()
  listedPrice: number;

  @Expose()
  attributes: object;

  @Expose()
  status: string;

  @Expose()
  description: string;

  @Expose()
  slug: string;

  @Expose()
  metaTitle: string;

  @Expose()
  metaDescription: string;

  @Expose()
  metaKeyword: string;

  @Expose()
  @Type(() => ProductImageResponse)
  images: ProductImageResponse[];

  @Expose()
  isNew: boolean;

  @Expose()
  isBestSale: boolean;

  @Expose()
  isDiscount: boolean;

  @Expose()
  weight: string;

  @Expose()
  dimensions: string;

  @Expose()
  imageSize: string;

  @Expose()
  variants: ProductResponse;

  constructor(data) {
    Object.assign(this, data);
  }
}
