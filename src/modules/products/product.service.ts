import { Model, PipelineStage, Types } from 'mongoose';
import { Injectable, NotFoundException } from '@nestjs/common';
import { CreateProductDto } from './dtos/create-product.dto';
import { Mode, OrderBy, ProductType } from '../../utils/enums';
import { SearchProductDto } from './dtos/search-product.dto';
import { Product } from './schemas/product.schema';
import { InjectModel } from '@nestjs/mongoose';
import { UpdateProductDto } from './dtos/update-product.dto';
import { Attribute } from '../attributes/schemas/attribute.schema';
import { Property } from '../properties/schemas/property.schema';
import { isUndefined, keyBy, omitBy } from 'lodash';
import { nanoid } from 'nanoid';
import { SearchNameProductDto } from './dtos/search-name-product.dto';
import { UpdatePartialProductDto } from './dtos/update-partial-product.dto';
import { toObjectId } from '../../utils/object-id';
import { configAggregateCountAll } from '../../utils/mongo.util';
import { MetadataResponse } from '../../utils/response/meta.response';
import { ProductResponse } from './response/product.response';
import { SearchParentResponse } from './response/search-parent.response';
import { plainToInstance } from 'class-transformer';
import { SearchParentDto } from './dtos/search-parent.dto';
import { ReorderProductDto } from './dtos/reorder-product.dto';
import { IndexGenerator } from 'fractional-indexing-jittered';
@Injectable()
export class ProductService {
  constructor(
    @InjectModel(Product.name) private productModel: Model<Product>,
    @InjectModel(Attribute.name) private attributeModel: Model<Attribute>,
    @InjectModel(Property.name) private propertyModel: Model<Property>,
  ) {}

  async search(search: SearchNameProductDto) {
    const options = {
      conditions: {
        productType: {
          $in: search.productType.length
            ? search.productType
            : [ProductType.child, ProductType.single, ProductType.combo],
        },
        productName: {
          $regex: search.productName,
          $options: 'i',
        },
        deletedAt: null,
      },
      paging: {
        limit: search.limit,
        page: (search.page - 1) * search.limit,
      },
    };

    const products = await this.productModel
      .find({
        ...options.conditions,
      })
      .limit(options.paging.limit)
      .skip(options.paging.page);

    return products;
  }

  async create(payload: CreateProductDto): Promise<Product> {
    const createProductDto = CreateProductDto.fromProduct(payload);

    const productCreated = await new this.productModel(createProductDto).save();

    const createChildsDto = CreateProductDto.fromProductChild(
      payload,
      toObjectId(productCreated._id),
    );
    const childsCreated = await this.productModel.create(createChildsDto);
    return {
      ...JSON.parse(JSON.stringify(productCreated)),
      childs: childsCreated,
    };
  }

  async update(payload: UpdateProductDto): Promise<Product> {
    const updateProductDto = UpdateProductDto.fromProduct(payload);

    const updated = await this.productModel.findOneAndUpdate(
      {
        _id: payload.id,
        deletedAt: null,
      },
      {
        $set: updateProductDto,
      },
      { new: true },
    );

    const updateChildsDto = UpdateProductDto.fromProductChild(
      payload,
      payload.id,
    );
    const newsChilds = updateChildsDto.filter(
      (child) => child.mode === Mode.new,
    );

    if (newsChilds.length) {
      await this.productModel.create(newsChilds);
    }

    const updateChilds = updateChildsDto.filter(
      (child) => child.mode === Mode.update,
    );
    if (updateChilds.length) {
      for (const child of updateChilds) {
        await this.productModel.findOneAndUpdate(
          {
            _id: toObjectId(child.id),
            deletedAt: null,
          },
          {
            $set: child,
          },
          { new: true },
        );
      }
    }

    const deleteChilds = updateChildsDto.filter(
      (child) => child.mode === Mode.delete,
    );
    if (deleteChilds.length) {
      for (const child of deleteChilds) {
        await this.productModel.findOneAndUpdate(
          {
            _id: toObjectId(child.id),
            deletedAt: null,
          },
          {
            $set: {
              deletedAt: new Date(),
              updatedAt: new Date(),
            },
          },
          { new: true },
        );
      }
    }

    return {
      ...JSON.parse(JSON.stringify(updated)),
      childs: newsChilds.concat(updateChilds),
    };
  }

  async updatePartial(payload: UpdatePartialProductDto): Promise<Product> {
    const updateProductDto = UpdatePartialProductDto.fromProduct(payload);

    const updated = await this.productModel.findOneAndUpdate(
      {
        _id: payload.id,
        deletedAt: null,
      },
      {
        $set: updateProductDto,
      },
      { new: true }, // return old value before update set false
    );

    return {
      ...JSON.parse(JSON.stringify(updated)),
    };
  }

  async searchParents(
    dto: SearchParentDto,
  ): Promise<MetadataResponse<SearchParentResponse[]>> {
    const page = Number(dto.page) || 1;
    const limit = Number(dto.limit) || 10;

    const query = await this.productModel
      .find({
        deletedAt: null,
        ...(dto.productName && {
          productName: {
            $regex: dto.productName,
            $options: 'i',
          },
        }),
        ...(dto.productType && {
          productType: dto.productType,
        }),
      })
      .limit(limit)
      .select({
        _id: 1,
        productName: 1,
        productType: 1,
      })
      .skip((page - 1) * limit)
      .sort({ createdAt: dto.order === OrderBy.asc ? 1 : -1 })
      .exec();

    return {
      data: query.map((product) =>
        plainToInstance(SearchParentResponse, product),
      ),
      metadata: {
        totalRows: query.length,
        page: page,
        limit: limit,
        numberOfPage: Math.ceil(query.length / limit),
      },
    };
  }

  /**
   * v2FindAndCountAll
   * Refactor function get all parents and variants product
   * 15-02-2025
   * <AUTHOR>
   * @param {SearchProductDto} searchProductDto
   * @returns {Promise<MetadataResponse<ProductResponse[]>>}
   */
  async v2FindAndCountAll(
    searchProductDto: SearchProductDto,
  ): Promise<MetadataResponse<ProductResponse[]>> {
    // Params Filter
    const page = Number(searchProductDto.page) || 1;
    const limit = Number(searchProductDto.limit) || 10;
    // Params Sort
    const sortType: Record<string, 1 | -1> = {
      asc: 1,
      desc: -1,
    };
    const createdAt = sortType[searchProductDto.createdAt] || sortType.desc;

    const sortOption = searchProductDto.tab
      ? {
          isNull: sortType.asc,
          [searchProductDto.tab]: sortType.asc,
        }
      : {
          createdAt: createdAt,
        };

    const pipeline: PipelineStage[] = [
      {
        $match: omitBy(
          {
            parentId: null,
            deletedAt: null,
            productType: searchProductDto.productType,
            categoryId: searchProductDto.categoryId,
          },
          (value) => isUndefined(value) || value === '',
        ),
      },
      {
        $project: {
          description: 0,
          metaDescription: 0,
          metaTitle: 0,
          isNull: 0,
        },
      },
      {
        $lookup: {
          from: 'products',
          localField: '_id',
          foreignField: 'parentId',
          as: 'variants',
        },
      },
      {
        $addFields: {
          variants: {
            $map: {
              input: '$variants',
              as: 'variant',
              in: {
                $mergeObjects: [
                  '$$variant',
                  { id: { $toString: '$$variant._id' } },
                ],
              },
            },
          },
        },
      },
      {
        $addFields: {
          variants: {
            $filter: {
              input: '$variants',
              as: 'variant',
              cond: {
                $and: [
                  { $eq: ['$$variant.deletedAt', null] },
                  {
                    $regexMatch: {
                      input: '$$variant.productName',
                      regex: searchProductDto.productName || '',
                      options: 'i',
                    },
                  },
                ],
              },
            },
          },
          ...(searchProductDto.tab
            ? {
                isNull: {
                  $cond: [{ $eq: [`$${searchProductDto.tab}`, null] }, 1, 0],
                },
              }
            : {}),
        },
      },
      {
        $match: {
          $or: [
            {
              productName: {
                $regex: searchProductDto.productName || '',
                $options: 'i',
              },
            },
            { variants: { $ne: [] } },
          ],
        },
      },
      {
        $sort: sortOption,
      },
      ...configAggregateCountAll(limit, page),
    ];
    const [query] = await this.productModel.aggregate(pipeline);
    return {
      data: query.rows,
      metadata: {
        totalRows: query.totalRows,
        page: page,
        limit: limit,
        numberOfPage: query.numberOfPage,
      },
    };
  }

  async delete(id: Types.ObjectId): Promise<boolean> {
    const found = await this.productModel.findOne(
      {
        _id: id,
        deletedAt: null,
      },
      {
        _id: 1,
        parentId: 1,
      },
    );

    if (!found) {
      throw new NotFoundException();
    }

    const updated = await this.productModel.updateOne(
      {
        deletedAt: null,
        ...(found.parentId
          ? {
              _id: toObjectId(id),
            }
          : {
              $or: [{ parentId: toObjectId(id) }, { _id: toObjectId(id) }],
            }),
      },
      {
        $set: {
          deletedAt: new Date(),
        },
      },
    );
    if (updated.matchedCount === 0) {
      throw new Error('No document found with the given ID.');
    }

    if (updated.modifiedCount === 0) {
      throw new Error('Update failed. No changes were made.');
    }
    if (!found.parentId) {
      await this.updateParentAttribute(String(id));
    } else {
      await this.updateParentAttribute(String(found.parentId));
    }

    return true;
  }

  getAttributeAndProperties = async (attributes: any) => {
    const attributesKey = Object.keys(attributes).filter(
      (attr) => attr !== 'undefined',
    );

    const queryAttributes = await this.attributeModel
      .find({
        _id: {
          $in: attributesKey,
        },
      })
      .lean();

    const properties = attributesKey.reduce((result, key) => {
      return { ...result, ...attributes[key].properties };
    }, {});

    const queryProperties = await this.propertyModel
      .find({
        _id: {
          $in: Object.keys(properties),
        },
      })
      .lean();

    const attributeKeyById = keyBy(queryAttributes, '_id');
    const propertiesKeyById = keyBy(queryProperties, '_id');
    const attributePropertySelected = attributesKey.reduce((result, key) => {
      const propertiesKey = Object.keys(attributes[key].properties);
      result[key] = {
        id: key,
        ...attributeKeyById[key],
        selectedProperty: propertiesKey.map((propertyKey) => ({
          id: propertyKey,
          ...propertiesKeyById[propertyKey],
        })),
        rowId: nanoid(7),
      };
      return result;
    }, {});
    return Object.values(attributePropertySelected);
  };

  async findOne(id: Types.ObjectId): Promise<Product | { childs: Product[] }> {
    const query = await this.productModel
      .findOne({
        _id: id,
        deletedAt: null,
      })
      .lean();
    if (!query) {
      throw new NotFoundException();
    }
    if (query.attributes) {
      query.attributes = await this.getAttributeAndProperties(query.attributes);
    }

    if (query.productType === ProductType.parent) {
      const childs = await this.productModel
        .find({
          parentId: query._id,
          deletedAt: null,
        })
        .exec();

      return {
        ...query,
        childs: childs,
      };
    }
    return query;
  }

  async updateParentAttribute(id: string): Promise<void> {
    const product = await this.productModel.findOne({
      _id: toObjectId(id),
    });

    const attributes = {};
    const productChilds = await this.productModel.find(
      {
        parentId: toObjectId(product.id),
        deletedAt: null,
      },
      {
        _id: 1,
        attributes: 1,
      },
    );
    if (productChilds.length) {
      for (const product of productChilds) {
        Object.values(product.attributes).map((valueAttribute) => {
          if (attributes[valueAttribute.id]) {
            Object.assign(attributes[valueAttribute.id].properties, {
              ...valueAttribute.properties,
            });
          } else {
            attributes[valueAttribute.id] = valueAttribute;
          }
        });
      }
    }
    await this.productModel.updateOne(
      {
        _id: id,
      },
      {
        $set: {
          attributes,
        },
      },
    );
  }

  async reorder(payload: ReorderProductDto): Promise<boolean> {
    await this.productModel.updateMany(
      {
        [payload.tab]: { $ne: null }, // lọc các trường không phải null
      },
      {
        $set: {
          [payload.tab]: null,
        },
      },
    );
    const generator = new IndexGenerator([]);
    const values = generator.nKeysStart(payload.ids.length);
    const bulkOps = payload.ids.map((id, index) => ({
      updateOne: {
        filter: { _id: new Types.ObjectId(id) },
        update: {
          $set: {
            [payload.tab]: values[index], // mỗi document +1ms
          },
        },
      },
    }));

    await this.productModel.bulkWrite(bulkOps);
    return true;
  }
}
