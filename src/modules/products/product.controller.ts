import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
  UseInterceptors,
} from '@nestjs/common';
import { ProductService } from './product.service';
import { CreateProductDto } from './dtos/create-product.dto';
import {
  ApiBadRequestResponse,
  ApiForbiddenResponse,
  ApiInternalServerErrorResponse,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiOperation,
  ApiParam,
  ApiTags,
  ApiUnauthorizedResponse,
} from '@nestjs/swagger';
import { RouteName } from '../../utils/enums';
import { getDescription, description } from '../../utils/descriptions';
import { SearchProductDto } from './dtos/search-product.dto';
import { Product } from './schemas/product.schema';
import { Types } from 'mongoose';
import { UpdateProductDto } from './dtos/update-product.dto';
import { SearchNameProductDto } from './dtos/search-name-product.dto';
import { UpdatePartialProductDto } from './dtos/update-partial-product.dto';
import { SerializeResponse } from '../../decorators/responses/serialize.decorator';
import { ProductResponse } from './response/product.response';
import { SerializerInterceptor } from '../../interceptors/serialize.interceptor';
import { MetadataResponse } from '../../utils/response/meta.response';
import { SearchParentResponse } from './response/search-parent.response';
import { SearchParentDto } from './dtos/search-parent.dto';
import { ReorderProductDto } from './dtos/reorder-product.dto';

@Controller(`/${RouteName.products}`)
@ApiTags(RouteName.products)
@ApiOkResponse({
  description: description.httpStatus.ok,
  isArray: true,
})
@ApiForbiddenResponse({
  description: description.httpStatus.forbidden,
})
@ApiUnauthorizedResponse({ description: description.httpStatus.unAuthorized })
@ApiBadRequestResponse({ description: description.httpStatus.badRequest })
@ApiInternalServerErrorResponse({
  description: description.httpStatus.interalServer,
})
@ApiNotFoundResponse({ description: description.httpStatus.notFound })
export class ProductController {
  constructor(private readonly productService: ProductService) {}

  @Get('search')
  @ApiOperation({
    summary: getDescription(RouteName.products, description.controller.gets),
  })
  async search(@Query() search: SearchNameProductDto): Promise<Product[]> {
    return this.productService.search(search);
  }

  @Get('search/parents')
  @ApiOperation({
    summary: getDescription(RouteName.products, description.controller.gets),
  })
  @SerializeResponse(SearchParentResponse)
  @UseInterceptors(SerializerInterceptor)
  async searchParents(
    @Query() query: SearchParentDto,
  ): Promise<MetadataResponse<SearchParentResponse[]>> {
    return this.productService.searchParents(query);
  }

  @Get()
  @ApiOperation({
    summary: getDescription(RouteName.products, description.controller.gets),
  })
  @SerializeResponse(ProductResponse)
  @UseInterceptors(SerializerInterceptor)
  async gets(
    @Query() searchProductDto: SearchProductDto,
  ): Promise<MetadataResponse<ProductResponse[]>> {
    return this.productService.v2FindAndCountAll(searchProductDto);
  }

  @Post()
  @ApiOperation({
    summary: getDescription(RouteName.products, description.controller.post),
  })
  async create(@Body() payload: CreateProductDto): Promise<Product> {
    return this.productService.create(payload);
  }

  @Put()
  @ApiOperation({
    summary: getDescription(RouteName.products, description.controller.put),
  })
  async update(@Body() payload: UpdateProductDto): Promise<Product> {
    return this.productService.update(payload);
  }

  @Put('/partial')
  @ApiOperation({
    summary: getDescription(RouteName.products, description.controller.put),
  })
  async updatePartial(
    @Body() payload: UpdatePartialProductDto,
  ): Promise<Product> {
    return this.productService.updatePartial(payload);
  }

  @Put('/reorder')
  @ApiOperation({
    summary: getDescription(RouteName.products, description.controller.put),
  })
  async reorder(@Body() payload: ReorderProductDto): Promise<boolean> {
    return this.productService.reorder(payload);
  }

  @Get('/:id')
  @ApiParam({
    name: 'id',
    type: 'string',
    required: true,
    description: description.products.validator.id,
  })
  @ApiOperation({
    summary: getDescription(RouteName.products, description.controller.getOne),
  })
  async get(
    @Param('id') id: Types.ObjectId,
  ): Promise<Product | { childs: Product[] }> {
    return this.productService.findOne(id);
  }

  @Delete('/:id')
  @ApiParam({
    name: 'id',
    type: 'string',
    required: true,
    description: description.categories.validator.id,
  })
  @ApiOperation({
    summary: getDescription(RouteName.products, description.controller.getOne),
  })
  async delete(@Param('id') id: Types.ObjectId): Promise<boolean> {
    return this.productService.delete(id);
  }
}
