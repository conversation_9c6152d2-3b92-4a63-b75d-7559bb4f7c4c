import { Module } from '@nestjs/common';
import { ProductController } from './product.controller';
import { ProductService } from './product.service';
import { MongooseModule } from '@nestjs/mongoose';
import { Product, ProductSchema } from './schemas/product.schema';
import {
  Attribute,
  AttributeSchema,
} from '../attributes/schemas/attribute.schema';
import {
  Property,
  PropertySchema,
} from '../properties/schemas/property.schema';
@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Product.name, schema: ProductSchema, collection: 'products' },
      {
        name: Attribute.name,
        schema: AttributeSchema,
        collection: 'attributes',
      },
      {
        name: Property.name,
        schema: PropertySchema,
        collection: 'properties',
      },
    ]),
  ],
  controllers: [ProductController],
  providers: [ProductService],
})
export class ProductModule {}
