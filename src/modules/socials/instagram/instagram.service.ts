import { BadRequestException, Injectable } from '@nestjs/common';
import { InstagramMedia } from './schema/instagram-media.schema';
import { Model } from 'mongoose';
import { InjectModel } from '@nestjs/mongoose';

@Injectable()
export class InstagramService {
  constructor(
    @InjectModel(InstagramMedia.name)
    private instagramMediModel: Model<InstagramMedia>,
  ) {}

  async upsertBulk(applicationId: string, media: InstagramMedia[]) {
    if (!applicationId) {
      throw new BadRequestException('applicationId is required');
    }

    const bulkOps = media.map((item) => ({
      updateOne: {
        filter: { mediaId: item.mediaId },
        update: { $set: item },
        upsert: true, // Nếu không tìm thấy thì tạo mới
      },
    }));

    await this.instagramMediModel.bulkWrite(bulkOps);

    return true;
  }
}
