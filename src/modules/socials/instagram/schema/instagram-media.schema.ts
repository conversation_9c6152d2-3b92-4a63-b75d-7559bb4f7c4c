import { HydratedDocument, Types } from 'mongoose';
import { configSchema } from '../../../../utils/schema';
import { BaseSchema } from '../../../shared/base/base.schema';
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';

export type InstagramMediaDocument = HydratedDocument<InstagramMedia>;

@Schema(configSchema)
export class InstagramMedia extends BaseSchema<InstagramMedia> {
  @Prop({
    type: String,
    required: true,
  })
  mediaId: string;

  @Prop({
    type: Types.ObjectId,
    required: true,
  })
  applicationId: Types.ObjectId;

  @Prop({
    type: String,
    required: true,
  })
  thumbnailUrl: string;

  @Prop({
    type: String,
    required: true,
  })
  mediaType: string;

  @Prop({
    type: String,
    required: true,
  })
  mediaUrl: string;

  @Prop({
    type: String,
    required: false,
  })
  caption: string;

  @Prop({
    type: String,
    required: false,
  })
  permaLink: string;
}

export const InstagramMediaSchema =
  SchemaFactory.createForClass(InstagramMedia);
