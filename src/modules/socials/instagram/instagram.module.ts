import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import {
  InstagramMedia,
  InstagramMediaSchema,
} from './schema/instagram-media.schema';
import { InstagramService } from './instagram.service';

@Module({
  imports: [
    MongooseModule.forFeature([
      {
        name: InstagramMedia.name,
        schema: InstagramMediaSchema,
        collection: 'instagram-medias',
      },
    ]),
  ],
  controllers: [],
  providers: [InstagramService],
  exports: [InstagramService],
})
export class InstagramModule {}
