import axios from 'axios';
import { InstagramField } from './utils';
import * as queryString from 'query-string';
import { Injectable } from '@nestjs/common';
import {
  OAuth2AccessTokenType,
  OAuth2EmbedType,
  OAuth2LongAccessTokenType,
} from '../types';

@Injectable()
export class InstagramApiService {
  constructor() {}

  private reversedInstagramField(): Record<string, string> {
    return Object.fromEntries(
      Object.entries(InstagramField).map(([key, value]) => [value, key]),
    );
  }

  private revertObject(data: Record<string, any> | Record<string, any>[]) {
    const reversedInstagramField = this.reversedInstagramField();

    if (Array.isArray(data)) {
      return data.map((item) => {
        return Object.keys(item).reduce((result: Record<string, any>, key) => {
          if (reversedInstagramField[key]) {
            result[reversedInstagramField[key]] = item[key]; // Đổi key thành giá trị đã hoán đổi
          }
          return result;
        }, {});
      });
    } else {
      return Object.keys(data).reduce((result: Record<string, any>, key) => {
        if (reversedInstagramField[key]) {
          result[reversedInstagramField[key]] = data[key]; // Đổi key thành giá trị đã hoán đổi
        }
        return result;
      }, {});
    }
  }

  private convertKey(key): string {
    return InstagramField[key] || key;
  }

  private convertObject(data: Record<string, any>): Record<string, any> {
    return Object.keys(data).reduce((result, key) => {
      result[this.convertKey(key)] = data[key];
      return result;
    }, {});
  }

  private convertFormData(data): FormData {
    const formData = new FormData();
    Object.keys(data).map((key) => {
      formData.append(this.convertKey(key), data[key]);
    });
    return formData;
  }

  public getEmbedLink(data: OAuth2EmbedType, state: string): string {
    const urlObject = this.convertObject({
      // enableFbLogin: 0,
      // forceAuthentication: 1,
      clientId: data.clientId,
      redirectUri: data.redirectUrl,
      responseType: data.responseType || 'code',
      scope:
        data.scope ||
        [
          'instagram_business_basic',
          'instagram_business_manage_messages',
          'instagram_business_manage_comments',
          'instagram_business_content_publish',
        ].join(','),
      state: state,
    });

    return `${data.authUrl}?` + queryString.stringify(urlObject);
  }

  public async getAccessToken(data: OAuth2AccessTokenType): Promise<string> {
    try {
      const formData = this.convertFormData({
        clientId: data.clientId,
        clientSecret: data.clientSecret,
        grantType: data.grantType,
        redirectUri: data.redirectUrl,
        code: data.code,
      });

      const response = await axios.post(data.tokenUrl, formData);

      return response.data[InstagramField.accessToken];
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  public async getLongLivedAccessToken(
    data: OAuth2LongAccessTokenType,
  ): Promise<{
    accessToken: string;
    expireIn: number;
  }> {
    try {
      const response = await axios.get(
        'https://graph.instagram.com/access_token',
        {
          params: this.convertObject({
            grantType: 'ig_exchange_token',
            clientSecret: data.clientSecret,
            accessToken: data.shortLivedToken,
          }),
        },
      );

      return {
        accessToken: response.data['access_token'],
        expireIn: response.data['expires_in'],
      };
    } catch (error) {
      console.error(
        'Error exchanging for long-lived token:',
        error.response?.data || error,
      );
      throw error;
    }
  }

  public async refreshAccessToken(longLivedToken: string): Promise<any> {
    try {
      const response = await axios.get(
        'https://graph.instagram.com/refresh_access_token',
        {
          params: this.convertObject({
            grantType: 'ig_refresh_token',
            accessToken: longLivedToken,
          }),
        },
      );

      return {
        accessToken: response.data['access_token'],
        expireIn: response.data['expires_in'],
      };
    } catch (error) {
      console.error(
        'Error refreshing Instagram token:',
        error.response?.data || error,
      );
      throw error;
    }
  }

  public async getInformation(accessToken: string): Promise<any> {
    try {
      const response = await axios.get(`https://graph.instagram.com/me`, {
        params: this.convertObject({
          fields: 'id,username',
          accessToken,
        }),
      });
      return response.data;
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  public async getImages(accessToken: string): Promise<any> {
    const fields = [
      'id',
      'caption',
      'media_type',
      'media_url',
      'thumbnail_url',
      'permalink',
    ];

    try {
      const response = await axios.get(`https://graph.instagram.com/me/media`, {
        params: this.convertObject({
          fields: fields.join(','),
          accessToken,
        }),
      });
      return response.data?.data
        ? this.revertObject(response.data.data).map((image) => ({
            ...image,
            mediaId: image.id,
          }))
        : [];
    } catch (error) {
      console.error('Error fetching Instagram images:', error);
      throw error;
    }
  }
}
