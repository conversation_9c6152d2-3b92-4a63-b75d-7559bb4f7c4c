import { Injectable } from '@nestjs/common';
import { InstagramApiService } from './instagram/instagram-api.service';
import { InstagramService } from './instagram/instagram.service';
import { InstagramMedia } from './instagram/schema/instagram-media.schema';

@Injectable()
export class SocialService {
  constructor(
    private instagramApiService: InstagramApiService,
    private instagramService: InstagramService,
  ) {}

  async getImageInstagram(accessToken: string) {
    return this.instagramApiService.getImages(accessToken);
  }

  async saveImageInstagram(id: string, images: InstagramMedia[]) {
    return this.instagramService.upsertBulk(id, images);
  }
}
