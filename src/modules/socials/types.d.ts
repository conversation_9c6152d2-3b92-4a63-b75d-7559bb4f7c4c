export type OAuth2EmbedType = {
  authUrl: string;
  clientId: string;
  redirectUrl: string;
  responseType: string;
  scope: string;
};

export type OAuth2AccessTokenType = {
  clientId: string;
  clientSecret: string;
  tokenUrl: string;
  redirectUrl: string;
  responseType: string;
  scope: string;
  code: string;
  grantType: string;
};

export type OAuth2LongAccessTokenType = {
  clientSecret: string;
  shortLivedToken: string;
};
