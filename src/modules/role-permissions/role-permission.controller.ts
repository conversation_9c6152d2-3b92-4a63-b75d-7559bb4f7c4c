import { Body, Controller, Delete, Get, Param, Post } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiParam } from '@nestjs/swagger';
import { RolePermissionService } from './role-permission.service';
import { CreateRolePermissionDto } from './dtos/create-role-permission.dto';
import { RolePermission } from './schemas/role-permission.schema';
import { Types } from 'mongoose';
import { AssignPermissionsDto } from './dtos/assign-permissions.dto';

@Controller('role-permissions')
@ApiTags('role-permissions')
export class RolePermissionController {
  constructor(private readonly rolePermissionService: RolePermissionService) {}

  @Post()
  @ApiOperation({ summary: 'Create a role-permission mapping' })
  async create(
    @Body() createDto: CreateRolePermissionDto,
  ): Promise<RolePermission> {
    return this.rolePermissionService.create(createDto);
  }

  @Get('role/:roleId')
  @ApiParam({ name: 'roleId', type: String })
  @ApiOperation({ summary: 'Get permissions by role ID' })
  async findByRoleId(
    @Param('roleId') roleId: Types.ObjectId,
  ): Promise<RolePermission[]> {
    return this.rolePermissionService.findByRoleId(roleId);
  }

  @Get('permission/:permissionId')
  @ApiParam({ name: 'permissionId', type: String })
  @ApiOperation({ summary: 'Get roles by permission ID' })
  async findByPermissionId(
    @Param('permissionId') permissionId: Types.ObjectId,
  ): Promise<RolePermission[]> {
    return this.rolePermissionService.findByPermissionId(permissionId);
  }

  @Delete('role/:roleId/permission/:permissionId')
  @ApiParam({ name: 'roleId', type: String })
  @ApiParam({ name: 'permissionId', type: String })
  @ApiOperation({ summary: 'Remove a role-permission mapping' })
  async remove(
    @Param('roleId') roleId: Types.ObjectId,
    @Param('permissionId') permissionId: Types.ObjectId,
  ): Promise<{ success: boolean }> {
    const result = await this.rolePermissionService.removeByRoleAndPermission(
      roleId,
      permissionId,
    );
    return { success: result };
  }

  @Post('assign')
  @ApiOperation({ summary: 'Assign multiple permissions to a role' })
  async assignPermissions(
    @Body() assignDto: AssignPermissionsDto,
  ): Promise<{ success: boolean }> {
    await this.rolePermissionService.assignPermissionsToRole(
      assignDto.roleId,
      assignDto.permissionIds,
    );
    return { success: true };
  }
}
