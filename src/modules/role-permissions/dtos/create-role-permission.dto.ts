import { ApiProperty } from '@nestjs/swagger';
import { IsMongoId, IsNotEmpty } from 'class-validator';
import { Types } from 'mongoose';
import { description } from '../../../utils/descriptions';

export class CreateRolePermissionDto {
  @ApiProperty({
    type: 'string',
    required: true,
    description: description.roles.validator.id,
  })
  @IsNotEmpty()
  @IsMongoId()
  roleId: Types.ObjectId;

  @ApiProperty({
    type: 'string',
    required: true,
    description: description.permissions.validator.id,
  })
  @IsNotEmpty()
  @IsMongoId()
  permissionId: Types.ObjectId;

  constructor(data: Partial<CreateRolePermissionDto>) {
    Object.assign(this, data);
  }
}
