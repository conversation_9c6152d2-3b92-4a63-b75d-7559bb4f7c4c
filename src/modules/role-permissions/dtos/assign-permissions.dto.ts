import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsMongoId, IsNotEmpty } from 'class-validator';
import { Types } from 'mongoose';

export class AssignPermissionsDto {
  @ApiProperty({
    type: 'string',
    required: true,
    description: 'Role ID',
  })
  @IsNotEmpty()
  @IsMongoId()
  roleId: Types.ObjectId;

  @ApiProperty({
    type: [String],
    required: true,
    description: 'Array of permission IDs',
  })
  @IsArray()
  @IsMongoId({ each: true })
  permissionIds: Types.ObjectId[];

  constructor(data: Partial<AssignPermissionsDto>) {
    Object.assign(this, data);
  }
}
