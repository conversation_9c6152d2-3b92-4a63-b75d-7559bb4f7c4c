import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { RolePermission } from './schemas/role-permission.schema';
import { CreateRolePermissionDto } from './dtos/create-role-permission.dto';
import { MetadataResponse } from '../../utils/response/meta.response';

@Injectable()
export class RolePermissionService {
  constructor(
    @InjectModel(RolePermission.name) private rolePermissionModel: Model<RolePermission>,
  ) {}

  async create(createDto: CreateRolePermissionDto): Promise<RolePermission> {
    const created = new this.rolePermissionModel(createDto);
    return created.save();
  }

  async findByRoleId(roleId: Types.ObjectId): Promise<RolePermission[]> {
    return this.rolePermissionModel
      .find({ roleId, deletedAt: null })
      .populate('permission')
      .exec();
  }

  async findByPermissionId(permissionId: Types.ObjectId): Promise<RolePermission[]> {
    return this.rolePermissionModel
      .find({ permissionId, deletedAt: null })
      .populate('role')
      .exec();
  }

  async removeByRoleAndPermission(
    roleId: Types.ObjectId,
    permissionId: Types.ObjectId,
  ): Promise<boolean> {
    const result = await this.rolePermissionModel.updateOne(
      { roleId, permissionId },
      { deletedAt: new Date() },
    );
    return result.modifiedCount > 0;
  }

  async removeByRoleId(roleId: Types.ObjectId): Promise<boolean> {
    const result = await this.rolePermissionModel.updateMany(
      { roleId },
      { deletedAt: new Date() },
    );
    return result.modifiedCount > 0;
  }

  async assignPermissionsToRole(
    roleId: Types.ObjectId,
    permissionIds: Types.ObjectId[],
  ): Promise<RolePermission[]> {
    // First remove existing permissions
    await this.removeByRoleId(roleId);
    
    // Then create new role-permission mappings
    const rolePermissions = permissionIds.map(permissionId => ({
      roleId,
      permissionId,
    }));
    
    return this.rolePermissionModel.insertMany(rolePermissions);
  }
}