import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument, Types } from 'mongoose';
import { BaseSchema } from '../../shared/base/base.schema';
import { configSchema } from '../../../utils/schema';

export type RolePermissionDocument = HydratedDocument<RolePermission>;

@Schema(configSchema)
export class RolePermission extends BaseSchema<RolePermission> {
  @Prop({
    type: Types.ObjectId,
    required: true,
    ref: 'Role',
  })
  roleId: Types.ObjectId;

  @Prop({
    type: Types.ObjectId,
    required: true,
    ref: 'Permission',
  })
  permissionId: Types.ObjectId;
}

export const RolePermissionSchema =
  SchemaFactory.createForClass(RolePermission);

RolePermissionSchema.virtual('role', {
  ref: 'Role',
  localField: 'roleId',
  foreignField: '_id',
  justOne: true,
});

RolePermissionSchema.virtual('permission', {
  ref: 'Permission',
  localField: 'permissionId',
  foreignField: '_id',
  justOne: true,
});

RolePermissionSchema.set('toObject', { virtuals: true });
RolePermissionSchema.set('toJSON', { virtuals: true });
