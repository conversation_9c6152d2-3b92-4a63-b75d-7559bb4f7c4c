import { AttributePropertySchema } from './../attribute-properties/schemas/attribute-property.schema';
import { Module } from '@nestjs/common';
import { AttributeController } from './attribute.controller';
import { AttributeService } from './attribute.service';
import { MongooseModule } from '@nestjs/mongoose';
import { Attribute, AttributeSchema } from './schemas/attribute.schema';
import { AttributeProperty } from '../attribute-properties/schemas/attribute-property.schema';
import {
  Property,
  PropertySchema,
} from '../properties/schemas/property.schema';
@Module({
  imports: [
    MongooseModule.forFeature([
      {
        name: Attribute.name,
        schema: AttributeSchema,
        collection: 'attributes',
      },
    ]),
    MongooseModule.forFeature([
      {
        name: AttributeProperty.name,
        schema: AttributePropertySchema,
        collection: 'attribute-properties',
      },
    ]),
    MongooseModule.forFeature([
      {
        name: Property.name,
        schema: PropertySchema,
        collection: 'properties',
      },
    ]),
  ],
  controllers: [AttributeController],
  providers: [AttributeService],
})
export class AttributeModule {}
