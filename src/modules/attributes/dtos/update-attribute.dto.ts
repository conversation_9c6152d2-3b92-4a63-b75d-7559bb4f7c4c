import { IsMongoId } from 'class-validator';
import { CreateAttributeDto } from './create-attribute.dto';
import { ApiProperty } from '@nestjs/swagger';
import { description } from '../../../utils/descriptions';
import { Types } from 'mongoose';

export class UpdateAttributeDto extends CreateAttributeDto {
  @ApiProperty({
    type: 'string',
    required: true,
    description: description.attributes.validator.id,
  })
  @IsMongoId()
  id: Types.ObjectId;
}
