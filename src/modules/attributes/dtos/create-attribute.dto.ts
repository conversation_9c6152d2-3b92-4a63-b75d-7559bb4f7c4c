import { ApiProperty } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsString,
  <PERSON><PERSON>ength,
  <PERSON><PERSON>ength,
  ValidateNested,
} from 'class-validator';
import { ValidatorFields } from '../../../utils/enums';
import { description } from '../../../utils/descriptions';
import { CreatePropertyDto } from './create-properties.dto';
import { Type } from 'class-transformer';

export class CreateAttributeDto {
  @ApiProperty({
    type: 'string',
    required: true,
    description: description.categories.validator.name,
  })
  @IsNotEmpty()
  @IsString()
  @MaxLength(ValidatorFields.strMaxLength)
  @MinLength(ValidatorFields.strMinLength)
  attributeName: string;

  @ApiProperty({ type: CreatePropertyDto, isArray: true })
  @ValidateNested({ each: true })
  @Type(() => CreatePropertyDto)
  properties: CreatePropertyDto[];

  constructor(data: Partial<CreateAttributeDto>) {
    Object.assign(this, data);
  }
}
