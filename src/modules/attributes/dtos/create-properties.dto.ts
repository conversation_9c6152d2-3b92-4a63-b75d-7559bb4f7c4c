import { ApiProperty } from '@nestjs/swagger';
import {
  IsHexColor,
  IsMongoId,
  IsNotEmpty,
  IsOptional,
  IsString,
  MaxLength,
  <PERSON>Length,
  ValidateIf,
} from 'class-validator';
import { description } from '../../../utils/descriptions';
import { CommonStatus, ValidatorFields } from '../../../utils/enums';

export class CreatePropertyDto {
  @ApiProperty({
    type: 'string',
    required: true,
    description: description.properties.validator.id,
  })
  @IsMongoId()
  id: string;

  @ApiProperty({
    type: 'string',
    required: true,
    description: description.properties.validator.name,
  })
  @IsNotEmpty()
  @IsString()
  @MaxLength(ValidatorFields.strMaxLength)
  @MinLength(ValidatorFields.strMinLength)
  propertyName: string;

  @ApiProperty({
    type: 'string',
    required: true,
    description: description.properties.validator.color,
  })
  @IsOptional()
  @IsString()
  @IsHexColor()
  @ValidateIf((field) => field.color)
  color: string;

  @ApiProperty({
    enum: Object.values(CommonStatus),
    default: CommonStatus.active,
    description: description.common.status,
    required: true,
  })
  @IsString()
  status: string;

  @ApiProperty({
    type: 'string',
    required: true,
    description: description.properties.validator.isNew,
  })
  @IsString()
  action: string;
}
