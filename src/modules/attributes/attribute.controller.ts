import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
} from '@nestjs/common';
import { AttributeService } from './attribute.service';
import { CreateAttributeDto } from './dtos/create-attribute.dto';
import {
  ApiBadRequestResponse,
  ApiForbiddenResponse,
  ApiInternalServerErrorResponse,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiOperation,
  ApiParam,
  ApiTags,
  ApiUnauthorizedResponse,
} from '@nestjs/swagger';
import { RouteName } from '../../utils/enums';
import { getDescription, description } from '../../utils/descriptions';
import { UpdateAttributeDto } from './dtos/update-attribute.dto';
import { Types } from 'mongoose';
import { Attribute } from './schemas/attribute.schema';
import { SearchAttributeDto } from './dtos/search-attribute.dto';
import { Property } from '../properties/schemas/property.schema';
import { MetadataResponse } from '../../utils/response/meta.response';

@Controller(`/${RouteName.attributes}`)
@ApiTags(RouteName.attributes)
@ApiOkResponse({
  description: description.httpStatus.ok,
  isArray: true,
})
@ApiForbiddenResponse({
  description: description.httpStatus.forbidden,
})
@ApiUnauthorizedResponse({ description: description.httpStatus.unAuthorized })
@ApiBadRequestResponse({ description: description.httpStatus.badRequest })
@ApiInternalServerErrorResponse({
  description: description.httpStatus.interalServer,
})
@ApiNotFoundResponse({ description: description.httpStatus.notFound })
export class AttributeController {
  constructor(private readonly attributeService: AttributeService) {}

  @Get()
  @ApiOperation({
    summary: getDescription(RouteName.attributes, description.controller.gets),
  })
  async gets(
    @Query() searchAttributeDto: SearchAttributeDto,
  ): Promise<MetadataResponse<Attribute[]>> {
    return this.attributeService.findAndCountAll(searchAttributeDto);
  }

  @Get('/:id')
  @ApiParam({
    name: 'id',
    type: 'string',
    required: true,
    description: description.attributes.validator.id,
  })
  @ApiOperation({
    summary: getDescription(
      RouteName.attributes,
      description.controller.getOne,
    ),
  })
  async get(@Param('id') id: Types.ObjectId): Promise<Attribute> {
    return this.attributeService.findOne(id);
  }

  @Post()
  @ApiOperation({
    summary: getDescription(RouteName.attributes, description.controller.post),
  })
  async create(@Body() payload: CreateAttributeDto): Promise<Attribute> {
    return this.attributeService.create(payload);
  }

  @Put()
  @ApiOperation({
    summary: getDescription(RouteName.attributes, description.controller.put),
  })
  async update(@Body() payload: UpdateAttributeDto): Promise<Attribute> {
    return this.attributeService.update(payload);
  }

  @Delete('/:id')
  @ApiParam({
    name: 'id',
    type: 'string',
    required: true,
    description: description.attributes.validator.id,
  })
  @ApiOperation({
    summary: getDescription(
      RouteName.attributes,
      description.controller.getOne,
    ),
  })
  async delete(@Param('id') id: Types.ObjectId): Promise<Attribute> {
    return this.attributeService.delete(id);
  }

  @Get('/:id/properties')
  @ApiParam({
    name: 'id',
    type: 'string',
    required: true,
    description: description.attributes.validator.id,
  })
  @ApiOperation({
    summary: getDescription(
      RouteName.attributes,
      description.controller.getOne,
    ),
  })
  async getPropertyByAtrributeId(
    @Param('id') id: Types.ObjectId,
  ): Promise<Property[]> {
    return this.attributeService.getPropertyByAtrributeId(id);
  }
}
