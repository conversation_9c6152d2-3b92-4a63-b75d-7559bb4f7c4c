import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument } from 'mongoose';
import { BaseSchema } from '../../shared/base/base.schema';

export type AttributeDocument = HydratedDocument<Attribute>;

@Schema({
  versionKey: false,
  virtuals: true,
  toJSON: {
    getters: true,
    transform: function (_, ret) {
      delete ret._id;
    },
  },
})
export class Attribute extends BaseSchema<Attribute> {
  @Prop({
    type: String,
    required: true,
  })
  attributeName: string;
}

export const AttributeSchema = SchemaFactory.createForClass(Attribute);
