import { Model, Types } from 'mongoose';
import { Injectable, NotFoundException } from '@nestjs/common';
import { CreateAttributeDto } from './dtos/create-attribute.dto';
import { UpdateAttributeDto } from './dtos/update-attribute.dto';
import { InjectModel } from '@nestjs/mongoose';
import { Attribute } from './schemas/attribute.schema';
import { AttributeProperty } from '../attribute-properties/schemas/attribute-property.schema';
import { Property } from '../properties/schemas/property.schema';
import { SearchAttributeDto } from './dtos/search-attribute.dto';
import { OrderBy } from '../../utils/enums';
import { keyBy } from 'lodash';
import { toObjectId } from '../../utils/object-id';
import { MetadataResponse } from '../../utils/response/meta.response';

@Injectable()
export class AttributeService {
  constructor(
    @InjectModel(Attribute.name) private attributeModel: Model<Attribute>,
    @InjectModel(AttributeProperty.name)
    private attributePropertyModel: Model<AttributeProperty>,
    @InjectModel(Property.name)
    private propertyModel: Model<Property>,
  ) {}

  async findAndCountAll(
    searchDto: SearchAttributeDto,
  ): Promise<MetadataResponse<Attribute[]>> {
    const page = Number(searchDto.page) || 1;
    const limit = Number(searchDto.limit) || 10;

    const conditions = {
      deletedAt: null,
    };

    const query = await this.attributeModel
      .find(conditions)
      .limit(limit)
      .skip((page - 1) * limit)
      .sort({ createdAt: searchDto.order === OrderBy.asc ? 1 : -1 })
      .exec();

    const count = await this.attributeModel.countDocuments(conditions);

    return {
      data: query,
      metadata: {
        totalRows: count,
        page: page,
        limit: limit,
        numberOfPage: Math.ceil(count / limit),
      },
    };
  }

  async findOne(
    id: Types.ObjectId,
  ): Promise<Attribute & { properties: Property[] }> {
    const attribute = await this.attributeModel.findById(id).exec();
    if (!attribute) {
      throw new NotFoundException();
    }

    const attributeProperties = await this.attributePropertyModel.find({
      attributeId: attribute._id,
    });

    const propertyEntities = await this.propertyModel.find({
      _id: attributeProperties.map((attribute) => attribute.propertyId),
      deletedAt: null,
    });

    const propertyKeyBy = keyBy(propertyEntities, '_id');

    const propertyBelongAttributes = attributeProperties
      .map((attribute) => {
        const property = propertyKeyBy[attribute.propertyId.toString()];
        if (!property) return;
        return {
          id: property._id,
          propertyName: property.propertyName,
          color: property.color,
          status: property.status,
        } as unknown as Property;
      })
      .filter(Boolean);

    return {
      ...attribute.toJSON(),
      properties: propertyBelongAttributes,
    };
  }

  async create(createCatDto: CreateAttributeDto): Promise<Attribute> {
    const created = await this.attributeModel.create({
      attributeName: createCatDto.attributeName,
    });
    const propertiesCreated = await this.propertyModel.create(
      createCatDto.properties.map((item) => ({
        propertyName: item.propertyName,
      })),
    );

    await this.attributePropertyModel.create(
      propertiesCreated.map((item) => ({
        attributeId: created._id,
        propertyId: item._id,
      })),
    );

    return created;
  }

  async update(payload: UpdateAttributeDto): Promise<Attribute> {
    const updated = await this.attributeModel.findOneAndUpdate(
      {
        _id: payload.id,
        deletedAt: null,
      },
      {
        $set: {
          attributeName: payload.attributeName,
        },
      },
      { new: true }, // return old value before update set false
    );

    const updatedProperties = payload.properties.filter(
      (property) => property.action === 'update',
    );
    if (updatedProperties) {
      for (const payload of updatedProperties) {
        await this.propertyModel.findOneAndUpdate(
          {
            _id: payload.id,
          },
          {
            $set: {
              propertyName: payload.propertyName,
              color: payload.color,
              status: payload.status,
            },
          },
          { new: true }, // return old value before update set false
        );
      }
    }

    const createProperties = payload.properties.filter(
      (property) => property.action === 'create',
    );
    if (createProperties.length) {
      const propertiesCreated = await this.propertyModel.create(
        createProperties.map((property) => ({
          propertyName: property.propertyName,
          color: property.color,
          status: property.status,
        })),
      );
      await this.attributePropertyModel.create(
        propertiesCreated.map((item) => ({
          propertyId: item._id,
          attributeId: payload.id,
        })),
      );
    }

    const removeProperties = payload.properties.filter(
      (property) => property.action === 'delete',
    );
    if (removeProperties.length) {
      await this.attributePropertyModel.deleteMany({
        propertyId: {
          $in: removeProperties
            .filter((item) => Types.ObjectId.isValid(item.id.toString()))
            .map((item) => toObjectId(item.id)),
        },
      });
      for (const remove of removeProperties) {
        await this.propertyModel.findOneAndUpdate(
          {
            _id: remove.id,
            deletedAt: null,
          },
          {
            $set: {
              deletedAt: new Date(),
            },
          },
          { new: true }, // return old value before update set false
        );
      }
    }

    return updated;
  }

  async delete(id: Types.ObjectId): Promise<Attribute> {
    const deleted = this.attributeModel.findOneAndUpdate(
      {
        _id: id,
        deletedAt: null,
      },
      {
        $set: {
          deletedAt: new Date(),
        },
      },
      { new: true }, // return old value before update set false
    );
    return deleted;
  }

  async getPropertyByAtrributeId(id: Types.ObjectId): Promise<Property[]> {
    // mongoose.set('debug', true);
    const attributeProperties = await this.attributePropertyModel
      .find({
        attributeId: id,
      })
      .exec();

    const properties = await this.propertyModel
      .find({
        _id: {
          $in: attributeProperties.map((item) => item.propertyId),
        },
      })
      .exec();
    return properties;
  }
}
