import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, MaxLength, MinLength } from 'class-validator';
import { description } from '../../../utils/descriptions';
import { ValidatorFields } from '../../../utils/enums';

export class LoginAuthDto {
  @ApiProperty({
    type: 'string',
    required: true,
    description: description.auths.validator.username,
  })
  @IsNotEmpty()
  @IsString()
  @MaxLength(ValidatorFields.strMaxLength)
  @MinLength(ValidatorFields.strMinLength)
  username: string;

  @ApiProperty({
    type: 'string',
    required: true,
    description: description.auths.validator.password,
  })
  @IsNotEmpty()
  @IsString()
  @MaxLength(ValidatorFields.strMaxLength)
  @MinLength(ValidatorFields.strMinLength)
  password: string;
}
