import {
  Body,
  Controller,
  Get,
  Post,
  UseGuards,
  Request,
  Query,
  Render,
} from '@nestjs/common';
import { RouteName } from '../../utils/enums';
import {
  ApiBadRequestResponse,
  ApiBearerAuth,
  ApiForbiddenResponse,
  ApiInternalServerErrorResponse,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiTags,
  ApiUnauthorizedResponse,
} from '@nestjs/swagger';
import { description } from '../../utils/descriptions';
import { AuthService } from './auth.service';
import { LoginAuthDto } from './dtos/login-auth.dto';
import { AuthGuard } from './auth.guard';
import { Public } from '../../decorators/auths/auth.decorator';
import { ApplicationService } from '../applications/applications.service';

@Controller(`/${RouteName.auths}`)
@ApiTags(RouteName.auths)
@ApiOkResponse({
  description: description.httpStatus.ok,
  isArray: true,
})
@ApiForbiddenResponse({
  description: description.httpStatus.forbidden,
})
@ApiUnauthorizedResponse({ description: description.httpStatus.unAuthorized })
@ApiBadRequestResponse({ description: description.httpStatus.badRequest })
@ApiInternalServerErrorResponse({
  description: description.httpStatus.interalServer,
})
@ApiNotFoundResponse({ description: description.httpStatus.notFound })
export class AuthController {
  constructor(
    private authService: AuthService,
    private applicationService: ApplicationService,
  ) {}

  @Post('login')
  @Public()
  signIn(@Body() signInDto: LoginAuthDto) {
    return this.authService.signIn(signInDto.username, signInDto.password);
  }

  @Get('instagram/call-back')
  @Public()
  @Render('index')
  async verifyInstagram(@Query() query: { code: string; state: string }) {
    const status = await this.applicationService.verifySocial(
      query.code,
      query.state,
    );

    return { status: status };
  }

  @UseGuards(AuthGuard)
  @Get('profile')
  @ApiBearerAuth('JWT-auth')
  getProfile(@Request() req) {
    return req.user;
  }
}
