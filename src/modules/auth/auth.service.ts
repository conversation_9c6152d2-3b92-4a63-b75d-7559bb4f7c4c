import { JwtService } from '@nestjs/jwt';
import {
  Injectable,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { UserService } from '../users/user.service';
import { comparePassword, generateRefreshToken } from '../../utils/bcrypt.util';
import { RoleService } from '../roles/role.service';
import { toObjectId } from '../../utils/object-id';

@Injectable()
export class AuthService {
  constructor(
    private userService: UserService,
    private roleService: RoleService,
    private jwtService: JwtService,
  ) {}

  async signIn(
    username: string,
    password: string,
  ): Promise<{
    accessToken: string;
    refreshToken: string;
  }> {
    const userEnity = await this.userService.findByUsername(username);
    if (!userEnity) {
      throw new NotFoundException('Tên đăng nhập hoặc mật khẩu không đúng');
    }
    if (!comparePassword(password, userEnity?.password)) {
      throw new UnauthorizedException();
    }
    const roleQuery = await this.roleService.findOne(
      toObjectId(userEnity.roleId),
    );
    if (!roleQuery) {
      throw new NotFoundException('Not found role');
    }

    const payload = {
      sub: userEnity._id,
      username: userEnity.username,
      role: roleQuery.roleName,
      permissions: [],
      id: String(userEnity._id),
    };

    return {
      accessToken: await this.jwtService.signAsync(payload),
      refreshToken: generateRefreshToken(),
    };
  }
}
