import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import mongoose, { HydratedDocument } from 'mongoose';

export type AttributePropertyDocument = HydratedDocument<AttributeProperty>;

@Schema({
  versionKey: false,
  virtuals: true,
  toJSON: {
    getters: true,
    transform: function (_, ret) {
      delete ret._id;
    },
  },
})
export class AttributeProperty {
  @Prop({
    type: String,
    required: true,
  })
  attributeId: string;

  @Prop({
    type: mongoose.Types.ObjectId,
    required: true,
  })
  propertyId: string;
}

export const AttributePropertySchema =
  SchemaFactory.createForClass(AttributeProperty);
