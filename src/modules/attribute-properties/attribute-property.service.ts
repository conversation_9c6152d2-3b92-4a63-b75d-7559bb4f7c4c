import { Model } from 'mongoose';
import { Injectable } from '@nestjs/common';
import { AttributeProperty } from './schemas/attribute-property.schema';
import { InjectModel } from '@nestjs/mongoose';

@Injectable()
export class AttributePropertyService {
  constructor(
    @InjectModel(AttributeProperty.name)
    private attributePropertyModel: Model<AttributeProperty>,
  ) {}

  async create(payload: AttributeProperty): Promise<AttributeProperty> {
    const created = new this.attributePropertyModel(payload);
    return created.save();
  }
}
