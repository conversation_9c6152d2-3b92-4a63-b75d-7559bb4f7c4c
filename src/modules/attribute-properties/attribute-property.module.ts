import { Modu<PERSON> } from '@nestjs/common';
import { AttributePropertyService } from './attribute-property.service';
import { MongooseModule } from '@nestjs/mongoose';
import {
  AttributeProperty,
  AttributePropertySchema,
} from './schemas/attribute-property.schema';
@Module({
  imports: [
    MongooseModule.forFeature([
      {
        name: AttributeProperty.name,
        schema: AttributePropertySchema,
        collection: 'attribute-properties',
      },
    ]),
  ],
  controllers: [],
  providers: [AttributePropertyService],
})
export class AttributePropertyModule {}
