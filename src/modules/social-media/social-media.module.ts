import { Modu<PERSON> } from '@nestjs/common';
import { SocialMediaController } from './social-media.controller';
import { SocialMediaService } from './social-media.service';
import { MongooseModule } from '@nestjs/mongoose';
import { SocialMedia, SocialMediaSchema } from './schemas/social-media.schema';

@Module({
  imports: [
    MongooseModule.forFeature([
      {
        name: SocialMedia.name,
        schema: SocialMediaSchema,
        collection: 'social-medias',
      },
    ]),
  ],
  controllers: [SocialMediaController],
  providers: [SocialMediaService],
  exports: [SocialMediaService],
})
export class SocialMediaModule {}
