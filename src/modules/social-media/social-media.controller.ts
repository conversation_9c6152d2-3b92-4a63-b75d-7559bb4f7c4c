import { Body, Controller, Get, Put } from '@nestjs/common';
import {
  ApiBadRequestResponse,
  ApiForbiddenResponse,
  ApiInternalServerErrorResponse,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiOperation,
  ApiTags,
  ApiUnauthorizedResponse,
} from '@nestjs/swagger';
import { RouteName } from '../../utils/enums';
import { getDescription, description } from '../../utils/descriptions';
import { UpdateSocialMediaDto } from './dtos';
import { SocialMediaService } from './social-media.service';
import { MetadataResponse } from '../../utils/response/meta.response';
import { SocialMedia } from './schemas/social-media.schema';

@Controller(`/${RouteName.socialMedias}`)
@ApiTags(RouteName.socialMedias)
@ApiOkResponse({
  description: description.httpStatus.ok,
  isArray: true,
})
@ApiForbiddenResponse({
  description: description.httpStatus.forbidden,
})
@ApiUnauthorizedResponse({ description: description.httpStatus.unAuthorized })
@ApiBadRequestResponse({ description: description.httpStatus.badRequest })
@ApiInternalServerErrorResponse({
  description: description.httpStatus.interalServer,
})
@ApiNotFoundResponse({ description: description.httpStatus.notFound })
export class SocialMediaController {
  constructor(private readonly socialMediaService: SocialMediaService) {}

  @Get()
  @ApiOperation({
    summary: getDescription(
      RouteName.socialMedias,
      description.controller.gets,
    ),
  })
  async gets(): Promise<MetadataResponse<SocialMedia[]>> {
    return this.socialMediaService.findAndCountAll();
  }

  @Put()
  @ApiOperation({
    summary: getDescription(RouteName.socialMedias, description.controller.put),
  })
  async update(@Body() payload: UpdateSocialMediaDto): Promise<SocialMedia> {
    return this.socialMediaService.bulkCreate(payload);
  }
}
