import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument, Types } from 'mongoose';
import { BaseSchema } from '../../shared/base/base.schema';

export type SocialMediaDocument = HydratedDocument<SocialMedia>;

@Schema({
  versionKey: false,
  virtuals: true,
  toJSON: {
    getters: true,
    transform: function (_, ret) {
      delete ret._id;
    },
  },
})
export class SocialMedia extends BaseSchema<SocialMedia> {
  @Prop({
    type: Types.ObjectId,
    required: true,
  })
  appId: Types.ObjectId;

  @Prop({
    type: String,
    required: true,
  })
  mediaId: string;

  @Prop({
    type: String,
    required: true,
  })
  mediaType: string;

  @Prop({
    type: String,
    required: false,
  })
  mediaUrl: string;

  @Prop({
    type: String,
    required: false,
  })
  thumbnailUrl: string;

  @Prop({
    type: String,
    required: true,
  })
  permaLink: string;
}

export const SocialMediaSchema = SchemaFactory.createForClass(SocialMedia);
