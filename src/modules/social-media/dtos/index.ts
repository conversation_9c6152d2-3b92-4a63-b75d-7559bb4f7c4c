import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsNotEmpty,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { description } from '../../../utils/descriptions';

class SocialMediaDto {
  @ApiProperty({
    type: 'string',
    required: true,
    description: 'Media ID',
  })
  @IsNotEmpty()
  @IsString()
  mediaId: string;

  @ApiProperty({
    type: 'string',
    required: true,
    description: 'Media type',
  })
  @IsNotEmpty()
  @IsString()
  mediaType: string;

  @ApiProperty({
    type: 'string',
    required: false,
    description: 'Media URL',
  })
  @IsOptional()
  @IsString()
  mediaUrl?: string;

  @ApiProperty({
    type: 'string',
    required: false,
    description: 'Thumbnail URL',
  })
  @IsOptional()
  @IsString()
  thumbnailUrl?: string;

  @ApiProperty({
    type: 'string',
    required: true,
    description: 'permaLink',
  })
  @IsNotEmpty()
  @IsString()
  permaLink: string;
}

export class UpdateSocialMediaDto {
  @ApiProperty({ type: SocialMediaDto, isArray: true })
  @ValidateNested({ each: true })
  @Type(() => SocialMediaDto)
  medias: SocialMediaDto[];

  @ApiProperty({
    type: 'string',
    required: true,
    description: description.shipFee.validator.name,
  })
  @IsNotEmpty()
  @IsString()
  appId: string;
}
