import { Model } from 'mongoose';
import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { SocialMedia } from './schemas/social-media.schema';
import { MetadataResponse } from '../../utils/response/meta.response';

@Injectable()
export class SocialMediaService {
  constructor(
    @InjectModel(SocialMedia.name)
    private socialMediaModel: Model<SocialMedia>,
  ) {}

  async findAndCountAll(): Promise<MetadataResponse<SocialMedia[]>> {
    const conditions = {
      deletedAt: null,
    };

    const query = await this.socialMediaModel
      .find(conditions)

      .exec();

    const count = await this.socialMediaModel.countDocuments(conditions);

    return {
      data: query,
      metadata: {
        totalRows: count,
        page: 1,
        limit: 12,
        numberOfPage: Math.ceil(count / 12),
      },
    };
  }

  async bulkCreate(payload): Promise<any> {
    await this.socialMediaModel.deleteMany({
      mediaId: {
        $nin: payload.medias.map((media) => media.mediaId),
      },
    });

    return this.socialMediaModel.bulkWrite(
      payload.medias.map((item) => ({
        updateOne: {
          filter: { mediaId: item.mediaId },
          update: {
            $setOnInsert: {
              ...item,
              appId: payload.appId,
            },
          },
          upsert: true,
        },
      })),
    );
  }
}
