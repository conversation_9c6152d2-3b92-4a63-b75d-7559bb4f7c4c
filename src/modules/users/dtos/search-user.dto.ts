import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional } from 'class-validator';
import { description } from '../../../utils/descriptions';
import { PageOptionsDto } from '../../../utils/pagination/dtos/page-option.dto';

export class SearchUserDto extends PageOptionsDto {
  @ApiProperty({
    type: 'string',
    required: false,
    description: description.users.validator.name,
  })
  @IsString()
  @IsOptional()
  username: string;

  constructor(data: Partial<SearchUserDto>) {
    super();
    Object.assign(this, data);
  }
}
