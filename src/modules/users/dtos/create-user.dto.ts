import { ApiProperty } from '@nestjs/swagger';
import {
  IsMongoId,
  IsNotEmpty,
  IsString,
  MaxLength,
  MinLength,
  Validate,
  ValidateIf,
} from 'class-validator';
import { CommonStatus, ValidatorFields } from '../../../utils/enums';
import { description } from '../../../utils/descriptions';

export class CreateUserDto {
  @ApiProperty({
    type: 'string',
    required: true,
    description: description.users.validator.name,
  })
  @IsNotEmpty()
  @IsString()
  @MaxLength(ValidatorFields.strMaxLength)
  @MinLength(ValidatorFields.strMinLength)
  username: string;

  @ApiProperty({
    type: 'string',
    required: true,
    description: description.users.validator.password,
  })
  @IsNotEmpty()
  @IsString()
  @MaxLength(ValidatorFields.strMaxLength)
  @MinLength(ValidatorFields.strMinLength)
  password: string;

  @ApiProperty({
    type: 'string',
    required: false,
    description: description.users.validator.roleId,
  })
  @ValidateIf((dto) => dto.roleId)
  @IsMongoId()
  roleId: string;

  @ApiProperty({
    enum: Object.values(CommonStatus),
    default: CommonStatus.active,
    description: description.common.status,
    required: true,
  })
  @IsString()
  status: string;

  @ApiProperty({
    type: 'string',
    required: false,
    description: description.users.validator.firstName,
  })
  @IsString()
  firstName: string;

  @ApiProperty({
    type: 'string',
    required: false,
    description: description.users.validator.lastName,
  })
  @IsString()
  lastName: string;

  @ApiProperty({
    type: 'string',
    required: false,
    description: description.users.validator.userEmail,
  })
  @IsString()
  userEmail: string;

  @ApiProperty({
    type: 'string',
    required: false,
    description: description.users.validator.userPhone,
  })
  @IsString()
  @ValidateIf((dto) => dto.userPhone)
  @Validate((dto) => /^(0[3|5|7|8|9])+([0-9]{8})$/.test(dto.userPhone))
  userPhone?: string;

  @ApiProperty({
    type: 'string',
    required: false,
    description: description.users.validator.userAddress,
  })
  @IsString()
  userAddress: string;

  constructor(data: Partial<CreateUserDto>) {
    Object.assign(this, data);
  }
}
