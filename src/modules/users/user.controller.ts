import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
} from '@nestjs/common';
import {
  ApiBadRequestResponse,
  ApiForbiddenResponse,
  ApiInternalServerErrorResponse,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiOperation,
  ApiParam,
  ApiTags,
  ApiUnauthorizedResponse,
} from '@nestjs/swagger';
import { RouteName } from '../../utils/enums';
import { getDescription, description } from '../../utils/descriptions';
import { Types } from 'mongoose';
import { SearchUserDto } from './dtos/search-user.dto';
import { UserService } from './user.service';
import { User } from './schemas/user.schema';
import { CreateUserDto } from './dtos/create-user.dto';
import { UpdateUserDto } from './dtos/update-user.dto';
import { MetadataResponse } from '../../utils/response/meta.response';
import { UserToken } from '../../decorators/auths/user.decorator';
import { MetaRequestUser } from '../../utils/request/meta.request';

@Controller(`/${RouteName.users}`)
@ApiTags(RouteName.users)
@ApiOkResponse({
  description: description.httpStatus.ok,
  isArray: true,
})
@ApiForbiddenResponse({
  description: description.httpStatus.forbidden,
})
@ApiUnauthorizedResponse({ description: description.httpStatus.unAuthorized })
@ApiBadRequestResponse({ description: description.httpStatus.badRequest })
@ApiInternalServerErrorResponse({
  description: description.httpStatus.interalServer,
})
@ApiNotFoundResponse({ description: description.httpStatus.notFound })
export class UserController {
  constructor(private readonly userService: UserService) {}

  @Get()
  @ApiOperation({
    summary: getDescription(RouteName.users, description.controller.gets),
  })
  async gets(
    @UserToken() userToken: MetaRequestUser,
    @Query() searchUserDto: SearchUserDto,
  ): Promise<MetadataResponse<User[]>> {
    return this.userService.findAndCountAll(searchUserDto, userToken);
  }

  @Post()
  @ApiOperation({
    summary: getDescription(RouteName.users, description.controller.post),
  })
  async create(@Body() payload: CreateUserDto): Promise<User> {
    return this.userService.create(payload);
  }

  @Put()
  @ApiOperation({
    summary: getDescription(RouteName.users, description.controller.put),
  })
  async update(@Body() payload: UpdateUserDto): Promise<User> {
    return this.userService.update(payload);
  }

  @Get('/:id')
  @ApiParam({
    name: 'id',
    type: 'string',
    required: true,
    description: description.users.validator.id,
  })
  @ApiOperation({
    summary: getDescription(RouteName.users, description.controller.getOne),
  })
  async get(@Param('id') id: Types.ObjectId): Promise<User> {
    return this.userService.findOne(id);
  }

  @Delete('/:id')
  @ApiParam({
    name: 'id',
    type: 'string',
    required: true,
    description: description.categories.validator.id,
  })
  @ApiOperation({
    summary: getDescription(RouteName.users, description.controller.getOne),
  })
  async delete(@Param('id') id: Types.ObjectId): Promise<User> {
    return this.userService.delete(id);
  }
}
