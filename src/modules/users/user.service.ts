import { Model, PipelineStage, Types } from 'mongoose';
import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { User } from './schemas/user.schema';
import { SearchUserDto } from './dtos/search-user.dto';
import { OrderBy } from '../../utils/enums';
import { CreateUserDto } from './dtos/create-user.dto';
import { UpdateUserDto } from './dtos/update-user.dto';
import { comparePassword, generateHashPassword } from '../../utils/bcrypt.util';
import { MetadataResponse } from '../../utils/response/meta.response';
import { MetaRequestUser } from '../../utils/request/meta.request';
import { toObjectId } from '../../utils/object-id';
import { configAggregateCountAll } from '../../utils/mongo.util';
import { isUndefined, omitBy } from 'lodash';

@Injectable()
export class UserService {
  constructor(@InjectModel(User.name) private userModel: Model<User>) {}

  async findAndCountAll(
    dto: SearchUserDto,
    userToken: MetaRequestUser,
  ): Promise<MetadataResponse<User[]>> {
    const options = {
      conditions: {
        deletedAt: null,
        _id: { $ne: toObjectId(userToken.id) },
        username: {
          $regex: dto.username || '',
          $options: 'i',
        },
      },
      paging: {
        limit: Number(dto.limit) || 10,
        page: Number(dto.page) || 1,
      },
    };

    const pipeline: PipelineStage[] = [
      {
        $match: options.conditions,
      },
      {
        $match: {
          username: { $ne: 'root' },
        },
      },
      {
        $lookup: {
          from: 'roles',
          localField: 'roleId',
          foreignField: '_id',
          as: 'role',
        },
      },
      {
        $unwind: {
          path: '$role',
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $sort: { createdAt: dto.order === OrderBy.asc ? 1 : -1 },
      },
      ...configAggregateCountAll(options.paging.limit, options.paging.page),
    ];
    const [query] = await this.userModel.aggregate(pipeline);
    const userEntities = query.rows.map((user) => {
      return {
        ...user,
        roleId: user.role?._id || null,
        roleName: user.role?.roleName || null,
      };
    });

    return {
      data: userEntities,
      metadata: {
        totalRows: query.totalRows,
        page: options.paging.page,
        limit: options.paging.limit,
        numberOfPage: Math.ceil(query.totalRows / options.paging.limit),
      },
    };
  }

  async findOne(id: Types.ObjectId): Promise<User> {
    return this.userModel.findById(id).exec();
  }

  async findByUsername(username: string): Promise<User> {
    return this.userModel.findOne({
      username: username,
      deletedAt: null,
    });
  }

  async create(dto: CreateUserDto): Promise<User> {
    const orConditions = [];

    if (dto.username) {
      orConditions.push({
        username: dto.username,
      });
    }
    if (dto.userEmail) {
      orConditions.push({
        userEmail: dto.userEmail,
      });
    }
    if (dto.userPhone) {
      orConditions.push({
        userPhone: dto.userPhone,
      });
    }

    const isExistUserName = await this.userModel.findOne({
      ...(orConditions.length && {
        $or: orConditions,
      }),
      deletedAt: null,
    });

    if (isExistUserName) {
      throw new Error('Tên đăng nhập, email hoặc số điện thoại đã tồn tại');
    }

    const password = generateHashPassword(dto.password);
    const created = new this.userModel({
      ...dto,
      roleId: toObjectId(dto.roleId),
      password: password,
    });

    return created.save();
  }

  async update(payload: UpdateUserDto): Promise<User> {
    const orConditions = [];

    if (payload.username) {
      orConditions.push({
        username: payload.username,
      });
    }
    if (payload.userEmail) {
      orConditions.push({
        userEmail: payload.userEmail,
      });
    }
    if (payload.userPhone) {
      orConditions.push({
        userPhone: payload.userPhone,
      });
    }

    const isExistUserName = await this.userModel.findOne({
      ...(orConditions.length && {
        $or: orConditions,
      }),
      deletedAt: null,
      _id: { $ne: toObjectId(payload.id) },
    });

    if (isExistUserName) {
      throw new Error('Tên đăng nhập, email hoặc số điện thoại đã tồn tại');
    }

    const userEnity = await this.userModel.findById(payload.id);

    let newPassword = '';
    const isMatching = comparePassword(payload.password, userEnity.password);
    if (!isMatching) {
      newPassword = generateHashPassword(payload.password);
    }
    const userUpdated = await this.userModel.findOneAndUpdate(
      {
        _id: payload.id,
        deletedAt: null,
      },
      {
        $set: omitBy(
          {
            ...payload,
            roleId: toObjectId(payload.roleId),
            ...(newPassword ? { password: newPassword } : {}),
          },
          (value) => isUndefined(value) || value === '',
        ),
      },
      { new: true },
    );

    return userUpdated;
  }

  async delete(id: Types.ObjectId): Promise<User> {
    const deleted = this.userModel.findOneAndUpdate(
      {
        _id: id,
        deletedAt: null,
      },
      {
        $set: {
          deletedAt: new Date(),
        },
      },
      { new: true },
    );
    return deleted;
  }
}
