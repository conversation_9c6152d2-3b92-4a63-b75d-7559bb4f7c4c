import { Injectable, OnModuleInit } from '@nestjs/common';
import { UserService } from './user.service';
import { CommonStatus } from '../../utils/enums';

@Injectable()
export class UserLoaderService implements OnModuleInit {
  constructor(private readonly userService: UserService) {}

  async onModuleInit() {
    const userEntity = await this.userService.findByUsername('root');
    if (!userEntity) {
      this.userService.create({
        username: 'root',
        password: 'Zaq@123456',
        roleId: '66e5d8b198c9301407527301',
        status: CommonStatus.active,
        firstName: 'root',
        lastName: 'root',
        userEmail: '<EMAIL>',
        userPhone: '0905xxxxxx',
        userAddress: '123 <PERSON><PERSON><PERSON><PERSON>, Quậ<PERSON> 1 , T<PERSON> <PERSON>',
      });
    }
  }
}
