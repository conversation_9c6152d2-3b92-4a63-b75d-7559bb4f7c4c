import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument, Types } from 'mongoose';
import { BaseSchema } from '../../shared/base/base.schema';
import { CommonStatus } from '../../../utils/enums';
import { configSchema } from '../../../utils/schema';

export type UserSchema = HydratedDocument<User>;

@Schema(configSchema)
export class User extends BaseSchema<User> {
  @Prop({
    type: String,
    required: true,
  })
  username: string;

  @Prop({
    type: String,
    required: true,
  })
  password: string;

  @Prop({
    type: String,
    required: false,
  })
  firstName: string;

  @Prop({
    type: String,
    required: false,
  })
  lastName: string;

  @Prop({
    type: String,
    required: false,
  })
  userEmail: string;

  @Prop({
    type: String,
  })
  userPhone: string;

  @Prop({
    type: String,
    required: false,
  })
  userAddress: string;

  @Prop({
    type: Types.ObjectId,
    required: true,
  })
  roleId: Types.ObjectId;

  @Prop({
    type: String,
    enum: Object.values(CommonStatus),
    default: CommonStatus.active,
    required: true,
  })
  status: string;

  @Prop({
    type: String,
    required: false,
  })
  googleId: string;
}

export const UserSchema = SchemaFactory.createForClass(User);

UserSchema.virtual('role', {
  ref: 'Role',
  localField: 'roleId',
  foreignField: '_id',
  justOne: true,
});

UserSchema.set('toObject', { virtuals: true });
UserSchema.set('toJSON', { virtuals: true });
