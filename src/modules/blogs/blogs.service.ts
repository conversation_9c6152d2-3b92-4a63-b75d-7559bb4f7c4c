import { Model, Types } from 'mongoose';
import { Injectable } from '@nestjs/common';
import { CreateBlogDto } from './dtos/create-blog.dto';
import { InjectModel } from '@nestjs/mongoose';
import { Blog } from './schemas/blogs.schema';
import { SearchBlogDto } from './dtos/search-blog.dto';
import { plainToInstance } from 'class-transformer';
import { BlogResponse } from './response/blog.response';
import { UpdateBlogDto } from './dtos/update-blog.dto';

@Injectable()
export class BlogService {
  constructor(@InjectModel(Blog.name) private modelBlog: Model<Blog>) {}

  async findAll(query: SearchBlogDto): Promise<BlogResponse[]> {
    const blogs = await this.modelBlog
      .find(
        {
          type: query.type,
          deletedAt: null,
        },
        {
          title: 1,
          slug: 1,
          authorId: 1,
          sortDescription: 1,
          createdAt: 1,
          type: 1,
          status: 1,
        },
      )
      .exec();

    return blogs.map((blog) => plainToInstance(BlogResponse, blog.toJSON()));
  }

  async findOne(id: Types.ObjectId): Promise<BlogResponse> {
    const blog = await this.modelBlog
      .findOne({
        _id: id,
        deletedAt: null,
      })
      .exec();
    return plainToInstance(BlogResponse, blog.toJSON());
  }

  async create(dto: CreateBlogDto): Promise<Blog> {
    const created = new this.modelBlog({
      ...dto,
      authorId: '66fcf9120bc01dfcbeabc235',
    });
    return created.save();
  }

  async update(payload: UpdateBlogDto): Promise<Blog> {
    const updated = this.modelBlog.findOneAndUpdate(
      {
        _id: payload.id,
        deletedAt: null,
      },
      {
        $set: {
          ...payload,
          authorId: '66fcf9120bc01dfcbeabc235',
        },
      },
      { new: true }, // return old value before update set false
    );
    return updated;
  }

  async delete(id: Types.ObjectId): Promise<Blog> {
    const deleted = this.modelBlog.findOneAndUpdate(
      {
        _id: id,
        deletedAt: null,
      },
      {
        $set: {
          deletedAt: new Date(),
        },
      },
      { new: true }, // return old value before update set false
    );
    return deleted;
  }
}
