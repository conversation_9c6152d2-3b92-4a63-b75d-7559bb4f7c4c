import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument } from 'mongoose';
import { BaseSchema } from '../../shared/base/base.schema';
import { BlogType, CommonStatus } from '../../../utils/enums';
import { configSchema } from '../../../utils/schema';

export type BlogDocument = HydratedDocument<Blog>;

@Schema(configSchema)
export class Blog extends BaseSchema<Blog> {
  @Prop({
    type: String,
    required: true,
  })
  title: string;

  @Prop({
    type: String,
    required: true,
    default: null,
  })
  slug: string;

  @Prop({
    type: String,
    required: false,
  })
  categoryId: string;

  @Prop({
    type: String,
    required: true,
    enum: Object.values(BlogType),
  })
  type: string;

  @Prop({
    type: String,
    required: true,
  })
  authorId: string;

  @Prop({
    type: String,
    required: false,
  })
  description: string;

  @Prop({
    type: String,
    required: false,
  })
  sortDescription: string;

  @Prop({
    type: String,
    required: false,
  })
  metaTitle: string;

  @Prop({
    type: String,
    required: false,
  })
  metaDescription: string;

  @Prop({
    type: Object,
    required: true,
    default: [],
  })
  images: object;

  @Prop({
    type: String,
    enum: Object.values(CommonStatus),
    default: CommonStatus.active,
    required: true,
  })
  status: string;
}

export const BlogSchema = SchemaFactory.createForClass(Blog);
