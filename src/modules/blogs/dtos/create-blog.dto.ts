import { ApiProperty } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsString,
  Length,
  ValidateIf,
  ValidateNested,
} from 'class-validator';
import { description } from '../../../utils/descriptions';
import { BlogType, CommonStatus, ValidatorFields } from '../../../utils/enums';
import { Type } from 'class-transformer';
import { ImageDto } from './image.dto';

export class CreateBlogDto {
  @ApiProperty({
    type: 'string',
    required: true,
    description: description.blogs.validator.title,
  })
  @IsNotEmpty()
  @IsString()
  @Length(ValidatorFields.strMinLength, ValidatorFields.strMaxLength)
  title: string;

  @ApiProperty({
    type: 'string',
    required: true,
    description: description.common.slug,
  })
  @IsNotEmpty()
  @IsString()
  slug: string;

  @ApiProperty({
    type: 'string',
    required: false,
    description: description.categories.validator.id,
  })
  @ValidateIf((dto) => dto.categoryId)
  @IsString()
  categoryId: string;

  @ApiProperty({
    enum: Object.values(BlogType),
    default: BlogType.news,
    required: true,
    description: description.blogs.validator.type,
  })
  @IsString()
  type: string;

  @ApiProperty({
    type: 'string',
    required: true,
    description: description.blogs.validator.description,
  })
  @IsString()
  @ValidateIf((field) => field.description)
  description: string;

  @ApiProperty({
    type: 'string',
    required: true,
    description: description.blogs.validator.sortDescription,
  })
  @IsString()
  @ValidateIf((field) => field.sortDescription)
  sortDescription: string;

  @ApiProperty({
    type: 'string',
    required: true,
    description: description.common.metaTitle,
  })
  @IsString()
  metaTitle: string;

  @ApiProperty({
    type: 'string',
    required: true,
    description: description.common.metaDescription,
  })
  @IsString()
  metaDescription: string;

  @ApiProperty({ type: ImageDto, isArray: true })
  @ValidateNested({ each: true })
  @Type(() => ImageDto)
  images: ImageDto[];

  @ApiProperty({
    enum: Object.values(CommonStatus),
    default: CommonStatus.active,
    description: description.common.status,
    required: true,
  })
  @IsString()
  status: string;

  constructor(data: Partial<CreateBlogDto>) {
    Object.assign(this, data);
  }
}
