import { ApiProperty } from '@nestjs/swagger';
import { PageOptionsDto } from '../../../utils/pagination/dtos/page-option.dto';
import { description } from '../../../utils/descriptions';
import { BlogType } from '../../../utils/enums';
import { IsNotEmpty, IsString } from 'class-validator';

export class SearchBlogDto extends PageOptionsDto {
  @ApiProperty({
    type: 'string',
    required: false,
    description: description.blogs.validator.type,
    enum: Object.values(BlogType),
    default: BlogType.news,
  })
  @IsString()
  @IsNotEmpty()
  type: string;

  constructor(data: Partial<SearchBlogDto>) {
    super();
    Object.assign(this, data);
  }
}
