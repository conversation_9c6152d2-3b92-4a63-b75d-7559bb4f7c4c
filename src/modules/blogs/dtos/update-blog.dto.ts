import { ApiProperty } from '@nestjs/swagger';
import { CreateBlogDto } from './create-blog.dto';
import { description } from '../../../utils/descriptions';
import { Types } from 'mongoose';
import { IsMongoId } from 'class-validator';

export class UpdateBlogDto extends CreateBlogDto {
  @ApiProperty({
    type: 'string',
    required: false,
    description: description.blogs.validator.id,
  })
  @IsMongoId()
  id: Types.ObjectId;

  constructor(data: Partial<UpdateBlogDto>) {
    super(data);
    Object.assign(this, data);
  }
}
