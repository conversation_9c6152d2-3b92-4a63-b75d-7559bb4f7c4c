import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
} from '@nestjs/common';
import { BlogService } from './blogs.service';
import { CreateBlogDto } from './dtos/create-blog.dto';
import {
  ApiBadRequestResponse,
  ApiForbiddenResponse,
  ApiInternalServerErrorResponse,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiOperation,
  ApiParam,
  ApiTags,
  ApiUnauthorizedResponse,
} from '@nestjs/swagger';
import { RouteName } from '../../utils/enums';
import { getDescription, description } from '../../utils/descriptions';
import { Blog } from './schemas/blogs.schema';
import { SearchBlogDto } from './dtos/search-blog.dto';
import { BlogResponse } from './response/blog.response';
import { Types } from 'mongoose';
import { UpdateBlogDto } from './dtos/update-blog.dto';

@Controller(`/${RouteName.blogs}`)
@ApiTags(RouteName.blogs)
@ApiOkResponse({
  description: description.httpStatus.ok,
  isArray: true,
})
@ApiForbiddenResponse({
  description: description.httpStatus.forbidden,
})
@ApiUnauthorizedResponse({ description: description.httpStatus.unAuthorized })
@ApiBadRequestResponse({ description: description.httpStatus.badRequest })
@ApiInternalServerErrorResponse({
  description: description.httpStatus.interalServer,
})
@ApiNotFoundResponse({ description: description.httpStatus.notFound })
export class BlogController {
  constructor(private readonly blogService: BlogService) {}

  @Get()
  @ApiOperation({
    summary: getDescription(RouteName.blogs, description.controller.gets),
  })
  async gets(@Query() query: SearchBlogDto): Promise<BlogResponse[]> {
    return this.blogService.findAll(query);
  }

  @Get('/:id')
  @ApiParam({
    name: 'id',
    type: 'string',
    required: true,
    description: description.blogs.validator.id,
  })
  @ApiOperation({
    summary: getDescription(RouteName.blogs, description.controller.getOne),
  })
  async get(@Param('id') id: Types.ObjectId): Promise<BlogResponse> {
    return this.blogService.findOne(id);
  }

  @Post()
  @ApiOperation({
    summary: getDescription(RouteName.blogs, description.controller.post),
  })
  async create(@Body() payload: CreateBlogDto): Promise<Blog> {
    return this.blogService.create(payload);
  }

  @Put()
  @ApiOperation({
    summary: getDescription(RouteName.blogs, description.controller.put),
  })
  async update(@Body() payload: UpdateBlogDto): Promise<Blog> {
    return this.blogService.update(payload);
  }

  @Delete('/:id')
  @ApiParam({
    name: 'id',
    type: 'string',
    required: true,
    description: description.blogs.validator.id,
  })
  @ApiOperation({
    summary: getDescription(RouteName.blogs, description.controller.getOne),
  })
  async delete(@Param('id') id: Types.ObjectId): Promise<Blog> {
    return this.blogService.delete(id);
  }
}
