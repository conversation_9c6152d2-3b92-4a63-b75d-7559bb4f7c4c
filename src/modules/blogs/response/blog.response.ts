import { Expose } from 'class-transformer';

export class BlogResponse {
  @Expose()
  id: string;

  @Expose()
  title: string;

  @Expose()
  type: string;

  @Expose()
  slug: string;

  @Expose()
  categoryId: string;

  description: string;
  @Expose()
  sortDescription: string;

  @Expose()
  metaTitle: string;

  @Expose()
  metaDescription: string;

  @Expose()
  status: string;

  @Expose()
  images: {
    id: string;
    url: string;
    position: string;
  }[];

  @Expose()
  createdAt: Date;

  @Expose()
  updatedAt: Date;

  deletedAt: Date;

  constructor(partial: Partial<BlogResponse>) {
    Object.assign(this, partial);
  }
}
