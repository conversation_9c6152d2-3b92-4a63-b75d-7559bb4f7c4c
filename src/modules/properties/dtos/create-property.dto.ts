import { ApiProperty } from '@nestjs/swagger';
import {
  IsHexColor,
  IsNotEmpty,
  IsOptional,
  IsString,
  MaxLength,
  MinLength,
} from 'class-validator';
import { ValidatorFields } from '../../../utils/enums';
import { description } from '../../../utils/descriptions';

export class CreatePropertyDto {
  @ApiProperty({
    type: 'string',
    required: true,
    description: description.properties.validator.name,
  })
  @IsNotEmpty()
  @IsString()
  @MaxLength(ValidatorFields.strMaxLength)
  @MinLength(ValidatorFields.strMinLength)
  propertyName: string;

  @ApiProperty({
    type: 'string',
    required: true,
    description: description.properties.validator.color,
  })
  @IsOptional()
  @IsHexColor()
  color: string;

  constructor(data: Partial<CreatePropertyDto>) {
    Object.assign(this, data);
  }
}
