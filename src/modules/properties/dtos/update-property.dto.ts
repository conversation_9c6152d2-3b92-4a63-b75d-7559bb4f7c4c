import { IsMongoId } from 'class-validator';
import { CreatePropertyDto } from './create-property.dto';
import { ApiProperty } from '@nestjs/swagger';
import { description } from '../../../utils/descriptions';

export class UpdatePropertyDto extends CreatePropertyDto {
  @ApiProperty({
    type: 'string',
    required: true,
    description: description.properties.validator.id,
  })
  @IsMongoId()
  id: number;
}
