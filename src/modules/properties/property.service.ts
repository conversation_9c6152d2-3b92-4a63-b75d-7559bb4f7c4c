import { Model, Types } from 'mongoose';
import { Injectable } from '@nestjs/common';
import { CreatePropertyDto } from './dtos/create-property.dto';
import { UpdatePropertyDto } from './dtos/update-property.dto';
import { Property } from './schemas/property.schema';
import { InjectModel } from '@nestjs/mongoose';

@Injectable()
export class PropertyService {
  constructor(
    @InjectModel(Property.name) private modelProperty: Model<Property>,
  ) {}

  async findAll(): Promise<Property[]> {
    return this.modelProperty
      .find({
        deletedAt: {
          // $not: {
          //   $eq: null,
          // },
          $eq: null,
        },
      })
      .exec();
  }

  async findOne(id: Types.ObjectId): Promise<Property> {
    return this.modelProperty.findById(id).exec();
  }

  async create(createCatDto: CreatePropertyDto): Promise<Property> {
    const created = new this.modelProperty(createCatDto);
    return created.save();
  }

  async update(payload: UpdatePropertyDto): Promise<Property> {
    const updated = this.modelProperty.findOneAndUpdate(
      {
        _id: payload.id,
        deletedAt: null,
      },
      {
        $set: payload,
      },
      { new: true }, // return old value before update set false
    );
    return updated;
  }

  async delete(id: Types.ObjectId): Promise<Property> {
    const deleted = this.modelProperty.findOneAndUpdate(
      {
        _id: id,
        deletedAt: null,
      },
      {
        $set: {
          deletedAt: new Date(),
        },
      },
      { new: true }, // return old value before update set false
    );
    return deleted;
  }
}
