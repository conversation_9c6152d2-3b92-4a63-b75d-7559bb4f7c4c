import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument } from 'mongoose';
import { CommonStatus } from '../../../utils/enums';
import { BaseSchema } from '../../shared/base/base.schema';

export type PropertyDocument = HydratedDocument<Property>;

@Schema({
  versionKey: false,
  virtuals: true,
  toJSON: {
    getters: true,
    transform: function (_, ret) {
      delete ret._id;
    },
  },
})
export class Property extends BaseSchema<Property> {
  @Prop({
    type: String,
    required: true,
  })
  propertyName: string;

  @Prop({
    type: String,
    required: false,
  })
  color: string;

  @Prop({
    type: String,
    enum: Object.values(CommonStatus),
    default: CommonStatus.active,
    required: true,
  })
  status: string;
}

export const PropertySchema = SchemaFactory.createForClass(Property);
