import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
} from '@nestjs/common';
import { PropertyService } from './property.service';
import { CreatePropertyDto } from './dtos/create-property.dto';
import {
  ApiBadRequestResponse,
  ApiForbiddenResponse,
  ApiInternalServerErrorResponse,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiOperation,
  ApiParam,
  ApiTags,
  ApiUnauthorizedResponse,
} from '@nestjs/swagger';
import { RouteName } from '../../utils/enums';
import { getDescription, description } from '../../utils/descriptions';
import { UpdatePropertyDto } from './dtos/update-property.dto';
import { Types } from 'mongoose';
import { PageOptionsDto } from '../../utils/pagination/dtos/page-option.dto';
import { Property } from './schemas/property.schema';

@Controller(`/${RouteName.properties}`)
@ApiTags(RouteName.properties)
@ApiOkResponse({
  description: description.httpStatus.ok,
  isArray: true,
})
@ApiForbiddenResponse({
  description: description.httpStatus.forbidden,
})
@ApiUnauthorizedResponse({ description: description.httpStatus.unAuthorized })
@ApiBadRequestResponse({ description: description.httpStatus.badRequest })
@ApiInternalServerErrorResponse({
  description: description.httpStatus.interalServer,
})
@ApiNotFoundResponse({ description: description.httpStatus.notFound })
export class PropertyController {
  constructor(private readonly propertyService: PropertyService) {}

  @Get()
  @ApiOperation({
    summary: getDescription(RouteName.properties, description.controller.gets),
  })
  async gets(@Query() pagination: PageOptionsDto): Promise<Property[]> {
    console.log('pagination properties', pagination);
    return this.propertyService.findAll();
  }

  @Get('/:id')
  @ApiParam({
    name: 'id',
    type: 'string',
    required: true,
    description: description.properties.validator.id,
  })
  @ApiOperation({
    summary: getDescription(
      RouteName.properties,
      description.controller.getOne,
    ),
  })
  async get(@Param('id') id: Types.ObjectId): Promise<Property> {
    return this.propertyService.findOne(id);
  }

  @Post()
  @ApiOperation({
    summary: getDescription(RouteName.properties, description.controller.post),
  })
  async create(@Body() payload: CreatePropertyDto): Promise<Property> {
    return this.propertyService.create(payload);
  }

  @Put()
  @ApiOperation({
    summary: getDescription(RouteName.properties, description.controller.put),
  })
  async update(@Body() payload: UpdatePropertyDto): Promise<Property> {
    return this.propertyService.update(payload);
  }

  @Delete('/:id')
  @ApiParam({
    name: 'id',
    type: 'string',
    required: true,
    description: description.properties.validator.id,
  })
  @ApiOperation({
    summary: getDescription(
      RouteName.properties,
      description.controller.getOne,
    ),
  })
  async delete(@Param('id') id: Types.ObjectId): Promise<Property> {
    return this.propertyService.delete(id);
  }
}
