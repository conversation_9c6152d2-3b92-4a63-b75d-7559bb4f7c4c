import { Injectable, Logger } from '@nestjs/common';
import * as fs from 'fs';
import * as path from 'path';
import { UploadFileDto } from './dtos/upload-file.dto';
import { nanoid } from 'nanoid';
import { ConfigService } from '@nestjs/config';
import { InjectModel } from '@nestjs/mongoose';
import { File } from './schema/file.schema';
import { Model } from 'mongoose';
import { CreateFileDto } from './dtos/create-file.dto';
import { UpdateFileDto } from './dtos/update-file.dto';
import { deleteFile, readdirAsync } from '../../utils/file.util';
import { DeleteFileDto } from './dtos/delete-file.dto';
import { keyBy } from 'lodash';
import { CloudinaryService } from '../cloudinary/cloudinary.service';
import { DeleteResult } from 'mongodb';

@Injectable()
export class FileService {
  private readonly logger = new Logger(FileService.name);
  private baseUrl;

  constructor(
    @InjectModel(File.name) private fileModel: Model<File>,
    private config: ConfigService,
    private cloudinaryService: CloudinaryService,
  ) {
    this.baseUrl = path.join(
      __dirname,
      `../../../${this.config.get<string>('UPLOAD_FOLDER')}/`,
    );
  }

  async uploadFiles(payload: UploadFileDto, files: Array<Express.Multer.File>) {
    const urlsPublic = [];
    const folderPath = `${this.baseUrl}${payload.page}`;

    // Ensure the folder exists, or create it
    if (!fs.existsSync(folderPath)) {
      fs.mkdirSync(folderPath, { recursive: true });
    }

    for (const file of files) {
      const { buffer } = file;
      const fileName = `${new Date().getTime()}-${nanoid(11)}.png`;
      const filePath = path.join(folderPath, fileName);
      fs.writeFileSync(filePath, buffer);
      urlsPublic.push(fileName);
    }

    return urlsPublic;
  }

  async uploadFilesCloud(
    payload: UploadFileDto,
    files: Array<Express.Multer.File>,
  ) {
    return this.cloudinaryService.uploadFileCloud(payload.page, files);
  }

  async bulkCreate(
    dto: CreateFileDto | CreateFileDto[],
  ): Promise<File | File[]> {
    const created = await this.fileModel.create(dto);
    return created;
  }

  async update(dto: UpdateFileDto): Promise<File> {
    const updated = await this.fileModel.findOneAndUpdate(
      {
        filename: dto.filename,
        deletedAt: null,
      },
      {
        $set: dto,
      },
      { new: true }, // return old value before update set false
    );
    return updated;
  }

  async deletePermanent(
    filename: string,
    dto: DeleteFileDto,
  ): Promise<DeleteResult> {
    const deleted = await this.fileModel.deleteOne({
      filename,
      page: dto.page,
      deletedAt: null,
    });
    return deleted;
  }

  async clearGarbage(): Promise<{ deleted: number }> {
    const folderPath = this.baseUrl;
    const folders = await readdirAsync(folderPath);
    let countFileDeleted = 0;

    for (const subFolder of folders) {
      const subFolderPath = `${folderPath}${subFolder}`;
      const files = await readdirAsync(subFolderPath);
      const fileEntities = await this.fileModel.find({
        filename: {
          $in: files,
        },
      });
      const keyByFileEntities = keyBy(fileEntities, 'filename');
      for (const file of files) {
        if (!keyByFileEntities[file.toString()]) {
          await deleteFile(`${subFolderPath}/${file}`);
          countFileDeleted++;
        }
      }
    }
    return {
      deleted: countFileDeleted,
    };
  }
}
