import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString } from 'class-validator';
import { description } from '../../../utils/descriptions';
import { FilePage } from '../../../utils/enums';

export class CreateFileDto {
  @ApiProperty({
    type: 'string',
    required: true,
    description: description.files.validator.name,
  })
  @IsNotEmpty()
  @IsString()
  filename: string;

  @ApiProperty({
    type: 'string',
    required: true,
    description: description.files.validator.referenceId,
  })
  @IsNotEmpty()
  @IsString()
  referenceId: string;

  @ApiProperty({
    type: 'string',
    required: true,
    description: description.files.validator.page,
    enum: Object.values(FilePage),
  })
  @IsNotEmpty()
  @IsString()
  page: string;

  constructor(data: Partial<CreateFileDto>) {
    Object.assign(this, data);
  }
}
