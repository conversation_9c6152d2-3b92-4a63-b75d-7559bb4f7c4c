import { ApiProperty } from '@nestjs/swagger';
import { description } from '../../../utils/descriptions';
import { IsNotEmpty, IsString } from 'class-validator';
import { FilePage } from '../../../utils/enums';

export class UploadFileDto {
  @ApiProperty({
    enum: Object.values(FilePage),
    description: description.files.validator.page,
    required: true,
  })
  @IsNotEmpty()
  @IsString()
  page: string;
}
