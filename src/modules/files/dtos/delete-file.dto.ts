import { FilePage } from './../../../utils/enums';
import { ApiProperty } from '@nestjs/swagger';
import { description } from '../../../utils/descriptions';
import { IsNotEmpty, IsString } from 'class-validator';

export class DeleteFileDto {
  @ApiProperty({
    type: 'string',
    required: true,
    description: description.files.validator.page,
    enum: Object.values(FilePage),
  })
  @IsString()
  @IsNotEmpty()
  page: string;
}
