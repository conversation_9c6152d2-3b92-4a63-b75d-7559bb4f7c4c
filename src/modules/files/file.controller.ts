import {
  Controller,
  Post,
  UseInterceptors,
  UploadedFiles,
  Body,
  Put,
  Delete,
  Param,
  Get,
} from '@nestjs/common';
import { FilesInterceptor } from '@nestjs/platform-express';

import {
  ApiBadRequestResponse,
  ApiForbiddenResponse,
  ApiInternalServerErrorResponse,
  ApiNotFoundResponse,
  ApiTags,
  ApiUnauthorizedResponse,
  ApiConsumes,
  ApiOkResponse,
  ApiOperation,
  ApiParam,
  ApiBody,
} from '@nestjs/swagger';

import { FilePage, RouteName } from '../../utils/enums';
import { description, getDescription } from '../../utils/descriptions';
import { ApiMultiFile } from '../../decorators/files/file-decorator';
import { FileService } from './file.service';
import { UploadFileDto } from './dtos/upload-file.dto';
import { CreateFileDto } from './dtos/create-file.dto';
import { UpdateFileDto } from './dtos/update-file.dto';
import { File } from './schema/file.schema';
import { DeleteFileDto } from './dtos/delete-file.dto';
import { DeleteResult } from 'mongodb';

@Controller(`/${RouteName.files}`)
@ApiTags(RouteName.files)
@ApiOkResponse({
  description: description.httpStatus.ok,
  isArray: true,
})
@ApiForbiddenResponse({
  description: description.httpStatus.forbidden,
})
@ApiUnauthorizedResponse({ description: description.httpStatus.unAuthorized })
@ApiBadRequestResponse({ description: description.httpStatus.badRequest })
@ApiInternalServerErrorResponse({
  description: description.httpStatus.interalServer,
})
@ApiNotFoundResponse({ description: description.httpStatus.notFound })
export class FileController {
  constructor(private readonly fileService: FileService) {}

  @Post('/upload')
  @UseInterceptors(FilesInterceptor('files'))
  @ApiConsumes('multipart/form-data')
  @ApiMultiFile()
  @ApiBody({
    schema: {
      type: 'object',
      required: ['page', 'files'],
      properties: {
        page: { type: 'string', enum: Object.values(FilePage) },
        files: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  async uploadFiles(
    @Body() payload: UploadFileDto,
    @UploadedFiles() files: Array<Express.Multer.File>,
  ) {
    return this.fileService.uploadFiles(payload, files);
  }

  @Post('/upload-cloud')
  @UseInterceptors(FilesInterceptor('files'))
  @ApiConsumes('multipart/form-data')
  @ApiMultiFile()
  @ApiBody({
    schema: {
      type: 'object',
      required: ['page', 'files'],
      properties: {
        page: { type: 'string', enum: Object.values(FilePage) },
        files: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  async uploadFilesCloud(
    @Body() payload: UploadFileDto,
    @UploadedFiles() files: Array<Express.Multer.File>,
  ) {
    return this.fileService.uploadFilesCloud(payload, files);
  }

  @Post()
  @ApiOperation({
    summary: getDescription(RouteName.files, description.controller.post),
  })
  async create(@Body() payload: CreateFileDto): Promise<File | File[]> {
    return this.fileService.bulkCreate(payload);
  }

  @Put()
  @ApiOperation({
    summary: getDescription(RouteName.files, description.controller.put),
  })
  async update(@Body() payload: UpdateFileDto): Promise<File> {
    return this.fileService.update(payload);
  }

  @Delete('/:filename')
  @ApiParam({
    name: 'filename',
    type: 'string',
    required: true,
    description: description.files.validator.id,
  })
  @ApiOperation({
    summary: getDescription(RouteName.files, description.controller.getOne),
  })
  async delete(
    @Param('filename') filename: string,
    @Body() body: DeleteFileDto,
  ): Promise<DeleteResult> {
    return this.fileService.deletePermanent(filename, body);
  }

  @Get('/garbage')
  @ApiOperation({
    summary: getDescription(RouteName.files, description.controller.clean),
  })
  async clearGarbage(): Promise<{ deleted: number }> {
    return this.fileService.clearGarbage();
  }
}
