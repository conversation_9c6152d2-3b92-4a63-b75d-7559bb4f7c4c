import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument } from 'mongoose';
import { FilePage } from '../../../utils/enums';
import { BaseSchema } from '../../shared/base/base.schema';

export type FileDocument = HydratedDocument<File>;

@Schema({
  versionKey: false,
  toJSON: {
    getters: true,
  },
})
export class File extends BaseSchema<File> {
  @Prop({
    type: String,
    required: true,
  })
  filename: string;

  @Prop({
    type: String,
    required: true,
  })
  referenceId: string;

  @Prop({
    type: String,
    required: true,
    enum: Object.values(FilePage),
    default: null,
  })
  page: string;
}

export const FileSchema = SchemaFactory.createForClass(File);
