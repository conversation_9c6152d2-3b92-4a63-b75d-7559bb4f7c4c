import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional } from 'class-validator';
import { description } from '../../../utils/descriptions';
import { PageOptionsDto } from '../../../utils/pagination/dtos/page-option.dto';

export class SearchRoleDto extends PageOptionsDto {
  @ApiProperty({
    type: 'string',
    required: false,
    description: description.roles.validator.roleName,
  })
  @IsString()
  @IsOptional()
  roleName: string;

  constructor(data: Partial<SearchRoleDto>) {
    super();
    Object.assign(this, data);
  }
}
