import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, <PERSON>Length, MinLength } from 'class-validator';
import { ValidatorFields } from '../../../utils/enums';
import { description } from '../../../utils/descriptions';

export class CreateRoleDto {
  @ApiProperty({
    type: 'string',
    required: true,
    description: description.roles.validator.roleName,
  })
  @IsNotEmpty()
  @IsString()
  @MaxLength(ValidatorFields.strMaxLength)
  @MinLength(3)
  roleName: string;

  constructor(data: Partial<CreateRoleDto>) {
    Object.assign(this, data);
  }
}
