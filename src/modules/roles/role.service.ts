import { Model, Types } from 'mongoose';
import { BadRequestException, Injectable } from '@nestjs/common';

import { InjectModel } from '@nestjs/mongoose';
import { Role } from './schemas/role.schema';
import { SearchRoleDto } from './dtos/search-role.dto';
import { OrderBy } from '../../utils/enums';
import { CreateRoleDto } from './dtos/create-role.dto';
import { UpdateRoleDto } from './dtos/update-role.dto';
import { MetadataResponse } from '../../utils/response/meta.response';

@Injectable()
export class RoleService {
  constructor(@InjectModel(Role.name) private roleModel: Model<Role>) {}

  async findAndCountAll(dto: SearchRoleDto): Promise<MetadataResponse<Role[]>> {
    const page = Number(dto.page) || 1;
    const limit = Number(dto.limit) || 10;
    const conditions = {
      deletedAt: null,
      ...(dto.roleName && {
        roleName: {
          $regex: dto.roleName,
          $options: 'i',
        },
      }),
    };
    const query = await this.roleModel
      .find(conditions)
      .limit(limit)
      .skip((page - 1) * limit)
      .sort({ createdAt: dto.order === OrderBy.asc ? 1 : -1 })
      .exec();

    const count = await this.roleModel.countDocuments(conditions);
    return {
      data: query,
      metadata: {
        totalRows: count,
        page: page,
        limit: limit,
        numberOfPage: Math.ceil(count / limit),
      },
    };
  }

  async findOne(id: Types.ObjectId): Promise<Role> {
    return this.roleModel.findById(id).exec();
  }

  async findOneByRoleName(roleName: string): Promise<Role> {
    return this.roleModel.findOne({
      roleName,
    });
  }

  async create(dto: CreateRoleDto): Promise<Role> {
    const created = new this.roleModel(dto);
    return created.save();
  }

  async createInit(dto: CreateRoleDto): Promise<Role> {
    const isExist = await this.roleModel.findOne({
      roleName: dto.roleName,
    });
    if (isExist) {
      throw new BadRequestException('Role already exist');
    }
    const created = new this.roleModel(dto);
    return created.save();
  }

  async update(payload: UpdateRoleDto): Promise<Role> {
    const isExist = await this.roleModel.findOne({
      roleName: payload.roleName,
      _id: { $ne: payload.id },
    });
    if (isExist) {
      throw new BadRequestException('Role already exist');
    }
    const updated = this.roleModel.findOneAndUpdate(
      {
        _id: payload.id,
        deletedAt: null,
      },
      {
        $set: payload,
      },
      { new: true }, // return old value before update set false
    );
    return updated;
  }

  async delete(id: Types.ObjectId): Promise<Role> {
    const deleted = this.roleModel.findOneAndUpdate(
      {
        _id: id,
        deletedAt: null,
      },
      {
        $set: {
          deletedAt: new Date(),
        },
      },
      { new: true }, // return old value before update set false
    );
    return deleted;
  }
}
