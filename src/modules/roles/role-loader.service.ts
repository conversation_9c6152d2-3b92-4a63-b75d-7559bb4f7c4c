import { Injectable, OnModuleInit } from '@nestjs/common';
import { toObjectId } from '../../utils/object-id';
import { RoleService } from './role.service';
import { CreateRoleDto } from './dtos/create-role.dto';

@Injectable()
export class RoleLoaderService implements OnModuleInit {
  constructor(private readonly roleService: RoleService) {}

  async onModuleInit() {
    const roleEntity = await this.roleService.findOneByRoleName('admin');
    if (!roleEntity) {
      this.roleService.create({
        _id: toObjectId('66e5d8b198c9301407527301'),
        roleName: 'admin',
      } as CreateRoleDto);
    }
  }
}
