import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
} from '@nestjs/common';
import {
  ApiBadRequestResponse,
  ApiForbiddenResponse,
  ApiInternalServerErrorResponse,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiOperation,
  ApiParam,
  ApiTags,
  ApiUnauthorizedResponse,
} from '@nestjs/swagger';
import { RouteName } from '../../utils/enums';
import { getDescription, description } from '../../utils/descriptions';
import { Types } from 'mongoose';
import { SearchRoleDto } from './dtos/search-role.dto';
import { RoleService } from './role.service';
import { Role } from './schemas/role.schema';
import { CreateRoleDto } from './dtos/create-role.dto';
import { UpdateRoleDto } from './dtos/update-role.dto';
import { MetadataResponse } from '../../utils/response/meta.response';

@Controller(`/${RouteName.roles}`)
@ApiTags(RouteName.roles)
@ApiOkResponse({
  description: description.httpStatus.ok,
  isArray: true,
})
@ApiForbiddenResponse({
  description: description.httpStatus.forbidden,
})
@ApiUnauthorizedResponse({ description: description.httpStatus.unAuthorized })
@ApiBadRequestResponse({ description: description.httpStatus.badRequest })
@ApiInternalServerErrorResponse({
  description: description.httpStatus.interalServer,
})
@ApiNotFoundResponse({ description: description.httpStatus.notFound })
export class RoleController {
  constructor(private readonly roleService: RoleService) {}

  @Get()
  @ApiOperation({
    summary: getDescription(RouteName.roles, description.controller.gets),
  })
  async gets(
    @Query() searchRoleDto: SearchRoleDto,
  ): Promise<MetadataResponse<Role[]>> {
    return this.roleService.findAndCountAll(searchRoleDto);
  }

  @Post()
  @ApiOperation({
    summary: getDescription(RouteName.roles, description.controller.post),
  })
  async create(@Body() payload: CreateRoleDto): Promise<Role> {
    return this.roleService.create(payload);
  }

  @Put()
  @ApiOperation({
    summary: getDescription(RouteName.roles, description.controller.put),
  })
  async update(@Body() payload: UpdateRoleDto): Promise<Role> {
    return this.roleService.update(payload);
  }

  @Get('/:id')
  @ApiParam({
    name: 'id',
    type: 'string',
    required: true,
    description: description.roles.validator.id,
  })
  @ApiOperation({
    summary: getDescription(RouteName.roles, description.controller.getOne),
  })
  async get(@Param('id') id: Types.ObjectId): Promise<Role> {
    return this.roleService.findOne(id);
  }

  @Delete('/:id')
  @ApiParam({
    name: 'id',
    type: 'string',
    required: true,
    description: description.categories.validator.id,
  })
  @ApiOperation({
    summary: getDescription(RouteName.roles, description.controller.getOne),
  })
  async delete(@Param('id') id: Types.ObjectId): Promise<Role> {
    return this.roleService.delete(id);
  }
}
