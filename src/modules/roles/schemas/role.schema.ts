import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument } from 'mongoose';
import { BaseSchema } from '../../shared/base/base.schema';
import { configSchema } from '../../../utils/schema';

export type RoleSchema = HydratedDocument<Role>;

@Schema(configSchema)
export class Role extends BaseSchema<Role> {
  @Prop({
    type: String,
    required: true,
  })
  roleName: string;
}

export const RoleSchema = SchemaFactory.createForClass(Role);
