import { <PERSON>du<PERSON> } from '@nestjs/common';
import { RoleController } from './role.controller';
import { RoleService } from './role.service';
import { MongooseModule } from '@nestjs/mongoose';
import { Role, RoleSchema } from './schemas/role.schema';
import { RoleLoaderService } from './role-loader.service';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Role.name, schema: RoleSchema, collection: 'roles' },
    ]),
  ],
  controllers: [RoleController],
  providers: [RoleService, RoleLoaderService],
  exports: [RoleService],
})
export class RoleModule {}
