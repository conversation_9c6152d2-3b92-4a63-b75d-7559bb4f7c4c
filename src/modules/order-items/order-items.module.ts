import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { OrderItem, OrderItemSchema } from './schemas/order-items.schema';
import { OrderItemService } from './order-items.service';
@Module({
  imports: [
    MongooseModule.forFeature([
      {
        name: OrderItem.name,
        schema: OrderItemSchema,
        collection: 'order-items',
      },
    ]),
  ],
  controllers: [],
  providers: [OrderItemService],
})
export class OrderItemModule {}
