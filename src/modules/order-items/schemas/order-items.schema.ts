import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument, Types } from 'mongoose';
import { BaseSchema } from '../../shared/base/base.schema';

export type OrderItemDocument = HydratedDocument<OrderItem>;

@Schema({
  versionKey: false,
  virtuals: true,
  toJSON: {
    getters: true,
    transform: function (_, ret) {
      delete ret._id;
    },
  },
})
export class OrderItem extends BaseSchema<OrderItem> {
  @Prop({
    type: Types.ObjectId,
    required: true,
  })
  orderId: Types.ObjectId;

  @Prop({
    type: Types.ObjectId,
    required: true,
  })
  productId: Types.ObjectId;

  @Prop({
    type: String,
    required: true,
  })
  productName: string;

  @Prop({
    type: Number,
    required: true,
  })
  currentPrice: number;

  @Prop({
    type: Number,
    required: true,
    default: 0,
  })
  quantity: number;

  @Prop({
    type: Number,
    required: true,
    default: 0,
  })
  discount: number;

  @Prop({
    type: Number,
    required: true,
    default: 0,
  })
  totalAmount: number;

  constructor(data: Partial<OrderItem>) {
    super();
    Object.assign(this, data);
  }
}

export const OrderItemSchema = SchemaFactory.createForClass(OrderItem);
