import { OrderItem } from '../../order-items/schemas/order-items.schema';

export class OrderResponse {
  id: string;

  customerId: string;

  customerName: string;

  customerPhone: string;

  customerAddress: string;

  customerCityId: string;

  customerDictrictId: string;

  customerWardId: string;

  receiverName: string;

  receiverPhone: string;

  receiverAddress: string;

  receiverCityId: string;

  receiverDictrictId: string;

  receiverWardId: string;

  totalAmount: number;

  totalDiscount: number;

  shipAmount: number;

  shipType: string;

  status: string;

  createdAt: Date;

  updatedAt: Date;

  orderItems: OrderItem[];

  constructor(data: Partial<OrderResponse>) {
    Object.assign(this, data);
  }
}
