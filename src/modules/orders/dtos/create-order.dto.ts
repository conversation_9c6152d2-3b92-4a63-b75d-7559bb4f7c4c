import { ApiProperty } from '@nestjs/swagger';
import { description } from '../../../utils/descriptions';
import { IsMongoId, IsNumber, IsString, ValidateNested } from 'class-validator';
import { CreateOrderItemDto } from './create-order-item.dto';
import { omit } from 'lodash';
import { Order } from '../schemas/order.schema';
import { Types } from 'mongoose';
import { OrderStatus, ShipType } from '../../../utils/enums';
import { Type } from 'class-transformer';

export class CreateOrderDto {
  @ApiProperty({
    type: 'string',
    required: false,
    description: description.customers.validator.id,
    default: '66c8275f1ac1f237c7e688dc',
  })
  @IsMongoId()
  customerId: Types.ObjectId;

  @ApiProperty({
    type: 'string',
    required: false,
    description: description.customers.validator.name,
    default: '<PERSON><PERSON><PERSON><PERSON>',
  })
  @IsString()
  customerName: string;

  @ApiProperty({
    type: 'string',
    required: false,
    description: description.customers.validator.phone,
    default: '0905123213',
  })
  @IsString()
  customerPhone: string;

  @ApiProperty({
    type: 'string',
    required: false,
    description: description.customers.validator.address,
    default:
      'Đường 100 Xô Viết Nghệ Tĩnh, Phường 21, Quận Bình Thạnh, TP Hồ Chí Minh',
  })
  @IsString()
  customerAddress: string;

  @ApiProperty({
    type: 'string',
    required: false,
    description: description.cities.validator.code,
    default: '79',
  })
  @IsString()
  customerCityId: string;

  @ApiProperty({
    type: 'string',
    required: false,
    description: description.dictricts.validator.code,
    default: '765',
  })
  @IsString()
  customerDictrictId: string;

  @ApiProperty({
    type: 'string',
    required: false,
    description: description.wards.validator.code,
    default: '26953',
  })
  @IsString()
  customerWardId: string;

  @ApiProperty({
    type: 'string',
    required: false,
    description: description.orders.validator.receiverName,
    default: 'Nguyễn Văn B',
  })
  @IsString()
  receiverName: string;

  @ApiProperty({
    type: 'string',
    required: false,
    description: description.orders.validator.receiverPhone,
    default: '0905123123',
  })
  @IsString()
  receiverPhone: string;

  @ApiProperty({
    type: 'string',
    required: false,
    description: description.orders.validator.receiverAddress,
    default:
      'Đường 19 Lã Xuân Oai, Phường Trường Thạnh, Quận 9, TP Hồ Chí Minh',
  })
  @IsString()
  receiverAddress: string;

  @ApiProperty({
    type: 'string',
    required: false,
    description: description.cities.validator.code,
    default: '79',
  })
  @IsString()
  receiverCityId: string;

  @ApiProperty({
    type: 'string',
    required: false,
    description: description.dictricts.validator.code,
    default: '769',
  })
  @IsString()
  receiverDictrictId: string;

  @ApiProperty({
    type: 'string',
    required: false,
    description: description.wards.validator.code,
    default: '26854',
  })
  @IsString()
  receiverWardId: string;

  @ApiProperty({
    type: 'number',
    required: false,
    description: description.orders.validator.totalAmount,
    default: 120000,
  })
  @IsNumber()
  totalAmount: number;

  @ApiProperty({
    type: 'number',
    required: false,
    description: description.orders.validator.totalDiscount,
    default: 20000,
  })
  @IsNumber()
  totalDiscount: number;

  @ApiProperty({
    type: 'number',
    required: false,
    description: description.orders.validator.shipAmount,
    default: 20000,
  })
  @IsNumber()
  shipAmount: number;

  @ApiProperty({
    enum: Object.values(ShipType),
    required: false,
    description: description.orders.validator.shipType,
    default: ShipType.cod,
  })
  @IsString()
  shipType: string;

  @ApiProperty({
    enum: Object.values(OrderStatus),
    required: true,
    description: description.orders.validator.status,
    default: OrderStatus.new,
  })
  @IsString()
  status: string;

  @ApiProperty({ type: CreateOrderItemDto, isArray: true })
  @ValidateNested({ each: true })
  @Type(() => CreateOrderItemDto)
  orderItems: CreateOrderItemDto[];

  constructor(data: Partial<CreateOrderDto>) {
    Object.assign(this, data);
  }

  public static fromOrderSchema(payload: CreateOrderDto): Order {
    return new Order({
      ...omit(payload, ['orderItems']),
    });
  }
}
