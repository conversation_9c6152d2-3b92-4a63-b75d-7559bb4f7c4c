import { ApiProperty } from '@nestjs/swagger';
import { description } from '../../../utils/descriptions';
import { IsMongoId, IsNumber, IsString } from 'class-validator';
import { Types } from 'mongoose';
import { OrderItem } from '../../order-items/schemas/order-items.schema';
import { toObjectId } from '../../../utils/object-id';

export class CreateOrderItemDto {
  @ApiProperty({
    type: 'string',
    required: false,
    description: description.products.validator.id,
    default: '66c8275f1ac1f237c7e688dc',
  })
  @IsMongoId()
  productId: Types.ObjectId;

  @ApiProperty({
    type: 'string',
    required: false,
    description: description.products.validator.name,
    default: 'Áo - XL - Đỏ',
  })
  @IsString()
  productName: string;

  @ApiProperty({
    type: 'number',
    required: false,
    description: description.orderItems.validator.quantity,
    default: 1,
  })
  @IsNumber()
  quantity: number;

  @ApiProperty({
    type: 'number',
    required: false,
    description: description.orderItems.validator.currentPrice,
    default: 100000,
  })
  @IsNumber()
  currentPrice: number;

  @ApiProperty({
    type: 'number',
    required: false,
    description: description.orderItems.validator.discount,
    default: 0,
  })
  @IsNumber()
  discount: number;

  constructor(data: Partial<CreateOrderItemDto>) {
    Object.assign(this, data);
  }

  public static fromOrderItems(
    orderItems: CreateOrderItemDto[],
    orderId: string,
  ): OrderItem[] {
    return orderItems.map(
      (orderItem) =>
        new OrderItem({
          ...orderItem,
          productId: toObjectId(orderItem.productId),
          orderId: toObjectId(orderId),
        }),
    );
  }
}
