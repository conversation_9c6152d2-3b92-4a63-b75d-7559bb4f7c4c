import { Modu<PERSON> } from '@nestjs/common';
import { OrderController } from './order.controller';
import { OrderService } from './order.service';
import { Order, OrderSchema } from './schemas/order.schema';
import { MongooseModule } from '@nestjs/mongoose';
import {
  OrderItem,
  OrderItemSchema,
} from '../order-items/schemas/order-items.schema';
@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Order.name, schema: OrderSchema, collection: 'orders' },
      {
        name: OrderItem.name,
        schema: OrderItemSchema,
        collection: 'order-items',
      },
    ]),
  ],
  controllers: [OrderController],
  providers: [OrderService],
})
export class OrderModule {}
