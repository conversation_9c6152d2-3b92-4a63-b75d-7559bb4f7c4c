import { Model, Types } from 'mongoose';
import { Injectable } from '@nestjs/common';
import { CreateOrderDto } from './dtos/create-order.dto';
import { UpdateOrderDto } from './dtos/update-order.dto';
import { Order } from './schemas/order.schema';
import { InjectModel } from '@nestjs/mongoose';
import { OrderItem } from '../order-items/schemas/order-items.schema';
import { CreateOrderItemDto } from './dtos/create-order-item.dto';
import { CreateOrderResponse } from './response/create-order-response';
import { OrderResponse } from './response/order-response';
import { SearchOrderDto } from './dtos/search-product.dto';
import { toObjectId } from '../../utils/object-id';
import { MetadataResponse } from '../../utils/response/meta.response';

@Injectable()
export class OrderService {
  constructor(
    @InjectModel(Order.name) private modelOrder: Model<Order>,
    @InjectModel(OrderItem.name) private modelOrderItem: Model<OrderItem>,
  ) {}

  async findAll(
    searchDto: SearchOrderDto,
  ): Promise<MetadataResponse<OrderResponse[]>> {
    const page = Number(searchDto.page) || 1;
    const limit = Number(searchDto.limit) || 10;

    const orderQuery = await this.modelOrder
      .find({
        deletedAt: null,
      })
      .limit(limit)
      .skip((page - 1) * limit)
      .exec();

    const orderCount = await this.modelOrder.countDocuments({
      deletedAt: null,
    });

    const orderItemQueries = await this.modelOrderItem.find({
      orderId: orderQuery.map((order) => order._id),
      deletedAt: null,
    });
    const orderItemKeyId = orderItemQueries.reduce(
      (result: Record<string, OrderItem[]>, current: OrderItem) => {
        const orderId = current.orderId.toString();
        result[orderId] = result[orderId] || [];
        result[orderId].push(current.toJSON() as OrderItem);
        return result;
      },
      {},
    );

    return {
      data: orderQuery.map(
        (order: Order) =>
          new OrderResponse({
            ...order.toJSON(),
            customerId: order.customerId.toString(),
            orderItems: orderItemKeyId[order._id.toString()],
          }),
      ),
      metadata: {
        totalRows: orderCount,
        page: page,
        limit: limit,
        numberOfPage: Math.ceil(orderCount / limit),
      },
    };
  }

  async create(payload: CreateOrderDto): Promise<CreateOrderResponse> {
    const createOrderDto = CreateOrderDto.fromOrderSchema(payload);

    const orderCreated: Order = await this.modelOrder.create(createOrderDto);

    const createOrderItemsDto: OrderItem[] = CreateOrderItemDto.fromOrderItems(
      payload.orderItems,
      orderCreated._id,
    );
    const orderItemBulkCreate: OrderItem[] = await this.modelOrderItem.create(
      createOrderItemsDto,
    );
    return new CreateOrderResponse({
      ...orderCreated.toJSON(),
      customerId: orderCreated.customerId.toString(),
      orderItems: orderItemBulkCreate,
    });
  }

  async update(
    payload: UpdateOrderDto,
  ): Promise<Order & { orderItems: OrderItem[] }> {
    const findOrder = await this.modelOrder.findById(payload.id);
    if (!findOrder) {
      throw new Error('Order not found');
    }

    await this.modelOrder.updateMany(
      {
        _id: payload.id,
      },
      {
        $set: payload,
      },
      { new: true },
    );

    await this.modelOrderItem.deleteMany({
      orderId: toObjectId(payload.id),
    });

    const createOrderItemsDto: OrderItem[] = CreateOrderItemDto.fromOrderItems(
      payload.orderItems,
      payload.id,
    );
    const orderItemBulkCreate: OrderItem[] = await this.modelOrderItem.create(
      createOrderItemsDto,
    );
    return {
      ...findOrder,
      ...payload,
      orderItems: orderItemBulkCreate,
    };
  }

  async findOne(
    id: Types.ObjectId,
  ): Promise<Order & { orderItems: OrderItem[] }> {
    const orderEntity = await this.modelOrder
      .findOne({
        _id: id,
        deletedAt: null,
      })
      .exec();
    const orderJson = orderEntity.toJSON();
    const orderItems = await this.modelOrderItem.find({
      orderId: orderEntity._id,
    });
    return {
      ...orderJson,
      orderItems: orderItems.map((orderItem) => orderItem.toJSON()),
    };
  }
}
