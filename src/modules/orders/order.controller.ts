import { UpdateOrderDto } from './dtos/update-order.dto';
import {
  Body,
  Controller,
  Get,
  Logger,
  Param,
  Post,
  Put,
  Query,
} from '@nestjs/common';
import { OrderService } from './order.service';
import { CreateOrderDto } from './dtos/create-order.dto';
import {
  ApiBadRequestResponse,
  ApiForbiddenResponse,
  ApiInternalServerErrorResponse,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiOperation,
  ApiParam,
  ApiTags,
  ApiUnauthorizedResponse,
} from '@nestjs/swagger';
import { RouteName } from '../../utils/enums';
import { getDescription, description } from '../../utils/descriptions';
import { Order } from './schemas/order.schema';
import { CreateOrderResponse } from './response/create-order-response';
import { SearchOrderDto } from './dtos/search-product.dto';
import { Types } from 'mongoose';

@Controller(`/${RouteName.orders}`)
@ApiTags(RouteName.orders)
@ApiOkResponse({
  description: description.httpStatus.ok,
  isArray: true,
})
@ApiForbiddenResponse({
  description: description.httpStatus.forbidden,
})
@ApiUnauthorizedResponse({ description: description.httpStatus.unAuthorized })
@ApiBadRequestResponse({ description: description.httpStatus.badRequest })
@ApiInternalServerErrorResponse({
  description: description.httpStatus.interalServer,
})
@ApiNotFoundResponse({ description: description.httpStatus.notFound })
export class OrderController {
  private readonly logger = new Logger(OrderController.name);

  constructor(private readonly orderService: OrderService) {}

  @Get()
  @ApiOperation({
    summary: getDescription(RouteName.orders, description.controller.gets),
  })
  async gets(@Query() searchDto: SearchOrderDto): Promise<any> {
    return this.orderService.findAll(searchDto);
  }

  @Post()
  @ApiOperation({
    summary: getDescription(RouteName.orders, description.controller.post),
  })
  async create(@Body() payload: CreateOrderDto): Promise<CreateOrderResponse> {
    return this.orderService.create(payload);
  }

  @Put()
  @ApiOperation({
    summary: getDescription(RouteName.orders, description.controller.put),
  })
  async update(@Body() payload: UpdateOrderDto): Promise<Order> {
    return this.orderService.update(payload);
  }

  @Get('/:id')
  @ApiParam({
    name: 'id',
    type: 'string',
    required: true,
    description: description.customers.validator.id,
  })
  @ApiOperation({
    summary: getDescription(RouteName.customers, description.controller.getOne),
  })
  async get(@Param('id') id: Types.ObjectId): Promise<Order> {
    return this.orderService.findOne(id);
  }
}
