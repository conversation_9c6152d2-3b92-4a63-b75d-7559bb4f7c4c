import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument, Types } from 'mongoose';
import { BaseSchema } from '../../shared/base/base.schema';
import { OrderStatus, ShipType } from '../../../utils/enums';

export type OrderDocument = HydratedDocument<Order>;

@Schema({
  versionKey: false,
  virtuals: true,
  toJSON: {
    getters: true,
    transform: function (_, ret) {
      delete ret._id;
    },
  },
})
export class Order extends BaseSchema<Order> {
  @Prop({
    type: Types.ObjectId,
    required: true,
  })
  customerId: Types.ObjectId;

  @Prop({
    type: String,
    required: true,
  })
  customerName: string;

  @Prop({
    type: String,
    required: true,
  })
  customerPhone;

  @Prop({
    type: String,
    required: true,
  })
  customerAddress: string;

  @Prop({
    type: String,
    required: true,
  })
  customerCityId: string;

  @Prop({
    type: String,
    required: true,
  })
  customerDictrictId: string;

  @Prop({
    type: String,
    required: true,
  })
  customerWardId: string;

  @Prop({
    type: String,
    required: false,
  })
  receiverName: string;

  @Prop({
    type: String,
    required: false,
  })
  receiverPhone: string;

  @Prop({
    type: String,
    required: false,
  })
  receiverAddress: string;

  @Prop({
    type: String,
    required: false,
  })
  receiverCityId: string;

  @Prop({
    type: String,
    required: false,
  })
  receiverDictrictId: string;

  @Prop({
    type: String,
    required: false,
  })
  receiverWardId: string;

  @Prop({
    type: Number,
    required: true,
    default: 0,
  })
  totalDiscount: number;

  @Prop({
    type: Number,
    required: true,
    default: 0,
  })
  shipAmount: number;

  @Prop({
    type: String,
    enum: Object.values(ShipType),
    required: true,
  })
  shipType: string;

  @Prop({
    type: Number,
    required: true,
    default: 0,
  })
  totalAmount: number;

  @Prop({
    type: String,
    enum: Object.values(OrderStatus),
    default: OrderStatus.new,
    required: true,
  })
  status: string;

  constructor(data: Partial<Order>) {
    super();
    Object.assign(this, data);
  }
}

export const OrderSchema = SchemaFactory.createForClass(Order);
