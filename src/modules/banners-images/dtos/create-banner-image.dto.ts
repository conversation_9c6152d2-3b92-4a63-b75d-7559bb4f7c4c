import { ApiProperty } from '@nestjs/swagger';
import {
  IsMongoId,
  IsNotEmpty,
  IsString,
  MaxLength,
  MinLength,
  ValidateIf,
} from 'class-validator';
import {
  BannerSectionType,
  CommonStatus,
  ValidatorFields,
} from '../../../utils/enums';
import { description } from '../../../utils/descriptions';

export class CreateBannerImageDto {
  @ApiProperty({
    type: 'string',
    required: true,
    description: description.categories.validator.name,
  })
  @IsNotEmpty()
  @IsString()
  @MaxLength(ValidatorFields.strMaxLength)
  @MinLength(ValidatorFields.strMinLength)
  bannerName: string;

  @ApiProperty({
    type: 'string',
    required: true,
    description: description.common.slug,
  })
  @MaxLength(ValidatorFields.strMaxLength)
  @MinLength(ValidatorFields.strMinLength)
  bannerCode: string;

  @ApiProperty({
    type: 'string',
    required: false,
    description: description.categories.validator.parent,
  })
  @ValidateIf((dto) => dto.parentId)
  @IsMongoId()
  parentId?: string;

  @ApiProperty({
    type: 'string',
    required: true,
    description: description.categories.validator.type,
    enum: Object.values(BannerSectionType),
  })
  @IsString()
  bannerType: string;

  @ApiProperty({
    enum: Object.values(CommonStatus),
    default: CommonStatus.active,
    description: description.common.status,
    required: true,
  })
  @IsString()
  status: string;

  constructor(data: Partial<CreateBannerImageDto>) {
    Object.assign(this, data);
  }
}
