import { ApiProperty } from '@nestjs/swagger';
import { CreateBannerImageDto } from './create-banner-image.dto';
import { description } from '../../../utils/descriptions';
import { IsMongoId } from 'class-validator';
export class UpdateBannerImageDto extends CreateBannerImageDto {
  @ApiProperty({
    type: 'string',
    required: true,
    description: description.categories.validator.id,
  })
  @IsMongoId()
  id: number;
}
