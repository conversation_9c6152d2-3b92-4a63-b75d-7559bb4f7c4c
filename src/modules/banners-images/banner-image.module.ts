import { Modu<PERSON> } from '@nestjs/common';
import { BannerImageController } from './banner-image.controller';
import { BannerImageService } from './banner-image.service';
import { MongooseModule } from '@nestjs/mongoose';
import { BannerImage, BannerImageSchema } from './schemas/banner-image.schema';

@Module({
  imports: [
    MongooseModule.forFeature([
      {
        name: BannerImage.name,
        schema: BannerImageSchema,
        collection: 'banner-images',
      },
    ]),
  ],
  controllers: [BannerImageController],
  providers: [BannerImageService],
})
export class BannerImageModule {}
