import { Model, Types } from 'mongoose';
import { Injectable } from '@nestjs/common';
import { CreateBannerImageDto } from './dtos/create-banner-image.dto';
import { UpdateBannerImageDto } from './dtos/update-banner-image.dto';
import { BannerImage } from './schemas/banner-image.schema';
import { InjectModel } from '@nestjs/mongoose';
import { PageOptionsDto } from '../../utils/pagination/dtos/page-option.dto';
import { MetadataResponse } from '../../utils/response/meta.response';

@Injectable()
export class BannerImageService {
  constructor(
    @InjectModel(BannerImage.name) private banner: Model<BannerImage>,
  ) {}

  async findAll(
    pagination: PageOptionsDto,
  ): Promise<MetadataResponse<BannerImage[]>> {
    const page = Number(pagination.page) || 1;
    const limit = Number(pagination.limit) || 10;

    const query = await this.banner
      .find({
        deletedAt: {
          // $not: {
          //   $eq: null,
          // },
          $eq: null,
        },
      })
      .limit(limit)
      .skip((page - 1) * limit)
      .exec();

    const queryTotal = await this.banner
      .find({
        deletedAt: {
          // $not: {
          //   $eq: null,
          // },
          $eq: null,
        },
      })
      .exec();
    return {
      data: query,
      metadata: {
        totalRows: queryTotal.length,
        page: page,
        limit: limit,
      },
    };
  }

  async findOne(id: Types.ObjectId): Promise<BannerImage> {
    return this.banner.findById(id).exec();
  }

  async create(createCatDto: CreateBannerImageDto): Promise<BannerImage> {
    const created = new this.banner(createCatDto);
    return created.save();
  }

  async update(payload: UpdateBannerImageDto): Promise<BannerImage> {
    const updated = this.banner.findOneAndUpdate(
      {
        _id: payload.id,
        deletedAt: null,
      },
      {
        $set: payload,
      },
      { new: true }, // return old value before update set false
    );
    return updated;
  }

  async delete(id: Types.ObjectId): Promise<BannerImage> {
    const deleted = this.banner.findOneAndUpdate(
      {
        _id: id,
        deletedAt: null,
      },
      {
        $set: {
          deletedAt: new Date(),
        },
      },
      { new: true }, // return old value before update set false
    );
    return deleted;
  }
}
