import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument } from 'mongoose';
import { BaseSchema } from '../../shared/base/base.schema';

export type BannerImageDocument = HydratedDocument<BannerImage>;

@Schema({
  versionKey: false,
  toJSON: {
    getters: true,
  },
})
export class BannerImage extends BaseSchema<BannerImage> {
  @Prop({
    type: String,
    required: true,
  })
  bannerPosition: string;

  @Prop({
    type: String,
    required: true,
  })
  bannerSectionId: string;

  @Prop({
    type: String,
    required: true,
  })
  directLink: string;

  @Prop({
    type: String,
    required: true,
  })
  heading: string;

  @Prop({
    type: String,
    required: true,
  })
  subHeading: string;

  @Prop({
    type: String,
    required: true,
  })
  subHeading2: string;

  @Prop({
    type: String,
    required: true,
  })
  subHeading3: string;

  @Prop({
    type: String,
    required: true,
  })
  markupText: string;

  @Prop({
    type: Number,
    required: true,
  })
  position: number;

  @Prop({
    type: String,
    required: true,
  })
  image: string;
}

export const BannerImageSchema = SchemaFactory.createForClass(BannerImage);
