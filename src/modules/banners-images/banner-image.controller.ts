import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
  UseInterceptors,
} from '@nestjs/common';
import { BannerImageService } from './banner-image.service';
import { CreateBannerImageDto } from './dtos/create-banner-image.dto';
import {
  ApiBadRequestResponse,
  ApiForbiddenResponse,
  ApiInternalServerErrorResponse,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiOperation,
  ApiParam,
  ApiTags,
  ApiUnauthorizedResponse,
} from '@nestjs/swagger';
import { RouteName } from '../../utils/enums';
import { getDescription, description } from '../../utils/descriptions';
import { UpdateBannerImageDto } from './dtos/update-banner-image.dto';
import { Types } from 'mongoose';
import { PageOptionsDto } from '../../utils/pagination/dtos/page-option.dto';
import { ResponseMessage } from '../../decorators/responses/message.decorator';
import { BannerResponse } from './responses/banner.response';
import { SerializeResponse } from '../../decorators/responses/serialize.decorator';
import { SerializerInterceptor } from '../../interceptors/serialize.interceptor';
import { MetadataResponse } from '../../utils/response/meta.response';

@Controller(`/${RouteName.bannerImages}`)
@ApiTags(RouteName.bannerImages)
@ApiOkResponse({
  description: description.httpStatus.ok,
  isArray: true,
})
@ApiForbiddenResponse({
  description: description.httpStatus.forbidden,
})
@ApiUnauthorizedResponse({ description: description.httpStatus.unAuthorized })
@ApiBadRequestResponse({ description: description.httpStatus.badRequest })
@ApiInternalServerErrorResponse({
  description: description.httpStatus.interalServer,
})
@ApiNotFoundResponse({ description: description.httpStatus.notFound })
export class BannerImageController {
  constructor(private readonly bannerImageService: BannerImageService) {}

  @Get()
  @SerializeResponse(BannerResponse)
  @ResponseMessage('Thành công')
  @ApiOperation({
    summary: getDescription(
      RouteName.bannerImages,
      description.controller.gets,
    ),
  })
  @UseInterceptors(SerializerInterceptor)
  async gets(
    @Query() pagination: PageOptionsDto,
  ): Promise<MetadataResponse<BannerResponse[]>> {
    return this.bannerImageService.findAll(pagination);
  }

  @Get('/:id')
  @ApiParam({
    name: 'id',
    type: 'string',
    required: true,
    description: description.bannerImages.validator.id,
  })
  @ApiOperation({
    summary: getDescription(
      RouteName.bannerImages,
      description.controller.getOne,
    ),
  })
  async get(@Param('id') id: Types.ObjectId): Promise<BannerResponse> {
    return this.bannerImageService.findOne(id);
  }

  @Post()
  @ApiOperation({
    summary: getDescription(
      RouteName.bannerImages,
      description.controller.post,
    ),
  })
  async create(@Body() payload: CreateBannerImageDto): Promise<BannerResponse> {
    return this.bannerImageService.create(payload);
  }

  @Put()
  @ApiOperation({
    summary: getDescription(RouteName.bannerImages, description.controller.put),
  })
  async update(@Body() payload: UpdateBannerImageDto): Promise<BannerResponse> {
    return this.bannerImageService.update(payload);
  }

  @Delete('/:id')
  @ApiParam({
    name: 'id',
    type: 'string',
    required: true,
    description: description.bannerImages.validator.id,
  })
  @ApiOperation({
    summary: getDescription(
      RouteName.bannerImages,
      description.controller.getOne,
    ),
  })
  async delete(@Param('id') id: Types.ObjectId): Promise<BannerResponse> {
    return this.bannerImageService.delete(id);
  }
}
