import { Controller, Get } from '@nestjs/common';
import { RouteName } from '../../utils/enums';
import { Public } from '../../decorators/auths/auth.decorator';
// import { SkipAuth } from 'src/auth/decorator';

@Controller(RouteName.healthz)
export class HealthController {
  constructor() {
    // empty construct
  }

  @Get()
  //   @SkipAuth()
  @Public()
  getHealthInfo() {
    return {
      ping: true,
    };
  }
}
