export class LocationVNResponse {
  id: string;
  code: string;
  name: string;
  cityCode?: string;
  districtCode?: string;

  constructor(data: Partial<LocationVNResponse>) {
    Object.assign(this, data);
  }
}

export class LocationGlobalResponse {
  id: string;
  code: string;
  name: string;
  countryCode?: string;
  stateCode?: string;
  latitude?: number;
  longitude?: number;

  constructor(data: Partial<LocationGlobalResponse>) {
    Object.assign(this, data);
  }
}
