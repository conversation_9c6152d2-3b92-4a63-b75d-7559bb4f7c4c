import { ApiProperty } from '@nestjs/swagger';
import { description } from '../../../utils/descriptions';
import { LocationGlobalType } from '../../../utils/enums';
import { IsOptional, IsString, ValidateIf } from 'class-validator';

export class SearchLocationGlobalDto {
  @ApiProperty({
    type: 'string',
    required: true,
    enum: LocationGlobalType,
    description: description.locations.validator.type,
  })
  @IsString()
  type: string;

  @ApiProperty({
    type: 'id',
    required: false,
    description: description.cities.validator.code,
  })
  @ValidateIf((dto) => dto.type === LocationGlobalType.states)
  @IsString()
  @IsOptional()
  countryCode: string;

  @ValidateIf((dto) => dto.type === LocationGlobalType.cities)
  @IsString()
  @IsOptional()
  stateCode: string;

  constructor(data: Partial<SearchLocationGlobalDto>) {
    Object.assign(this, data);
  }
}
