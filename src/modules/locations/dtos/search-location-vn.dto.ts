import { ApiProperty } from '@nestjs/swagger';
import { description } from '../../../utils/descriptions';
import { LocationVNType } from '../../../utils/enums';
import { IsNotEmpty, IsString, ValidateIf } from 'class-validator';

export class SearchLocationVNDto {
  @ApiProperty({
    type: 'string',
    required: true,
    enum: LocationVNType,
    description: description.locations.validator.type,
  })
  @IsString()
  @IsNotEmpty()
  type: string;

  @ApiProperty({
    type: 'id',
    required: false,
    description: description.cities.validator.code,
  })
  @ValidateIf((dto) => dto.type === LocationVNType.districts)
  @IsString()
  @IsNotEmpty()
  cityCode: string;

  @ApiProperty({
    type: 'id',
    required: false,
    description: description.dictricts.validator.code,
  })
  @ValidateIf((dto) => dto.type === LocationVNType.wards)
  @IsString()
  @IsNotEmpty()
  districtCode: string;

  constructor(data: Partial<SearchLocationVNDto>) {
    Object.assign(this, data);
  }
}
