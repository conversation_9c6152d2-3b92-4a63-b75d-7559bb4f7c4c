import { Model } from 'mongoose';
import { BadRequestException, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';

import { LocationGlobalType, LocationVNType } from '../../utils/enums';
import { VNCity } from './schemas/vn-city.schema';
import { VNDistrict } from './schemas/vn-district.schema';
import { VNWard } from './schemas/vn-ward.schema';
import { GlobalCity } from './schemas/global-city.schema';
import { GlobalCountry } from './schemas/global-country.schema';
import { GlobalState } from './schemas/global-state.schema';
import { SearchLocationVNDto } from './dtos/search-location-vn.dto';
import {
  LocationGlobalResponse,
  LocationVNResponse,
} from './dtos/response/location-reponse.dto';
import { SearchLocationGlobalDto } from './dtos/search.location-global.dto';

@Injectable()
export class LocationService {
  constructor(
    @InjectModel(VNCity.name) private vnCityModel: Model<VNCity>,
    @InjectModel(VNDistrict.name) private vnDistrictModel: Model<VNDistrict>,
    @InjectModel(VNWard.name) private vnWardModel: Model<VNWard>,
    @InjectModel(GlobalCountry.name)
    private globalCountryModel: Model<GlobalCountry>,
    @InjectModel(GlobalCity.name)
    private globalCityModel: Model<GlobalCity>,
    @InjectModel(GlobalState.name)
    private globalStateModel: Model<GlobalState>,
  ) {}

  async getLocationsGlobal(
    search: SearchLocationGlobalDto,
  ): Promise<LocationGlobalResponse[]> {
    switch (search.type) {
      case LocationGlobalType.countries:
        const countries = await this.globalCountryModel.find();
        return countries.map((country) => {
          return new LocationGlobalResponse({
            id: country.code,
            code: country.code,
            name: country.name,
            latitude: country.latitude,
            longitude: country.longitude,
          });
        });
      case LocationGlobalType.states:
        const states = await this.globalStateModel.find({
          countryCode: search.countryCode,
        });
        return states.map((state) => {
          return new LocationGlobalResponse({
            id: state.code,
            code: state.code,
            name: state.name,
            countryCode: state.countryCode,
            latitude: state.latitude,
            longitude: state.longitude,
          });
        });
      case LocationGlobalType.cities:
        const cities = await this.globalCityModel.find({
          stateCode: search.stateCode,
        });
        return cities.map((city) => {
          return new LocationGlobalResponse({
            id: city.code,
            code: city.code,
            name: city.name,
            stateCode: city.stateCode,
            latitude: city.latitude,
            longitude: city.longitude,
          });
        });
      default:
        throw new BadRequestException('Invalid location type');
    }
  }

  async getLocationsVN(
    search: SearchLocationVNDto,
  ): Promise<LocationVNResponse[]> {
    switch (search.type) {
      case LocationVNType.cities:
        const cities = await this.vnCityModel.find();
        return cities.map((city) => {
          return new LocationVNResponse({
            id: city.code,
            code: city.code,
            name: city.name,
          });
        });
      case LocationVNType.districts:
        const districts = await this.vnDistrictModel.find({
          cityCode: search.cityCode,
        });
        return districts.map((district) => {
          return new LocationVNResponse({
            id: district.code,
            code: district.code,
            name: district.name,
            cityCode: district.cityCode,
          });
        });
      case LocationVNType.wards:
        const wards = await this.vnWardModel.find({
          districtCode: search.districtCode,
        });
        return wards.map((ward) => {
          return new LocationVNResponse({
            id: ward.code,
            code: ward.code,
            name: ward.name,
            districtCode: ward.districtCode,
          });
        });
      default:
        throw new BadRequestException('Invalid location type');
    }
  }

  async getLocationsVNByCodes(
    type: LocationVNType,
    codes: string[],
  ): Promise<LocationVNResponse[]> {
    switch (type) {
      case LocationVNType.cities:
        const cities = await this.vnCityModel.find({
          code: { $in: codes },
        });
        return cities.map((city) => {
          return new LocationVNResponse({
            id: city.id,
            code: city.code,
            name: city.name,
          });
        });
      case LocationVNType.districts:
        const districts = await this.vnDistrictModel.find({
          code: { $in: codes },
        });
        return districts.map((district) => {
          return new LocationVNResponse({
            id: district.id,
            code: district.code,
            name: district.name,
            cityCode: district.cityCode,
          });
        });
      case LocationVNType.wards:
        const wards = await this.vnWardModel.find({
          code: { $in: codes },
        });
        return wards.map((ward) => {
          return new LocationVNResponse({
            id: ward.id,
            code: ward.code,
            name: ward.name,
            districtCode: ward.districtCode,
          });
        });
      default:
        throw new BadRequestException('Invalid location type');
    }
  }
}
