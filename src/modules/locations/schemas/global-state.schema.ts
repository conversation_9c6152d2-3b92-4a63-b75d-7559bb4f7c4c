import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument } from 'mongoose';

export type GlobalStateDocument = HydratedDocument<GlobalState>;

@Schema({
  versionKey: false,
  virtuals: true,
  toJSON: {
    getters: true,
    transform: function (_, ret) {
      delete ret._id;
    },
  },
})
export class GlobalState {
  @Prop({
    type: String,
    required: true,
    index: true,
    unique: true,
  })
  code: string;

  @Prop({
    type: String,
    required: true,
  })
  name: string;

  @Prop({
    type: String,
    required: true,
    index: true,
  })
  countryCode: string;

  @Prop({
    type: Number,
    required: false,
  })
  latitude: number;

  @Prop({
    type: Number,
    required: false,
  })
  longitude: number;
}

export const GlobalStateSchema = SchemaFactory.createForClass(GlobalState);
