import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument } from 'mongoose';
import { configSchema } from '../../../utils/schema';

export type VNCityDocument = HydratedDocument<VNCity>;

@Schema(configSchema)
export class VNCity {
  @Prop({
    type: String,
    required: true,
    index: true,
    unique: true,
  })
  code: string;

  @Prop({
    type: String,
    required: true,
  })
  name: string;

  @Prop({
    type: String,
    required: false,
    index: true,
  })
  countryCode: string;
}

export const VNCitySchema = SchemaFactory.createForClass(VNCity);
