import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument } from 'mongoose';

export type VNWardDocument = HydratedDocument<VNWard>;

@Schema({
  versionKey: false,
  virtuals: true,
  toJSON: {
    getters: true,
    transform: function (_, ret) {
      delete ret._id;
    },
  },
})
export class VNWard {
  @Prop({
    type: String,
    required: true,
    index: true,
    unique: true,
  })
  code: string;

  @Prop({
    type: String,
    required: true,
  })
  name: string;

  @Prop({
    type: String,
    required: true,
    index: true,
  })
  districtCode: string;
}

export const VNWardSchema = SchemaFactory.createForClass(VNWard);
