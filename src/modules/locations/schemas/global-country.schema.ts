import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument } from 'mongoose';

export type GlobalCountryDocument = HydratedDocument<GlobalCountry>;

@Schema({
  versionKey: false,
  virtuals: true,
  toJSON: {
    getters: true,
    transform: function (_, ret) {
      delete ret._id;
    },
  },
})
export class GlobalCountry {
  @Prop({
    type: String,
    required: true,
    index: true,
    unique: true,
  })
  code: string;

  @Prop({
    type: String,
    required: true,
  })
  name: string;

  @Prop({
    type: Number,
    required: false,
  })
  latitude: number;

  @Prop({
    type: Number,
    required: false,
  })
  longitude: number;
}

export const GlobalCountrySchema = SchemaFactory.createForClass(GlobalCountry);
