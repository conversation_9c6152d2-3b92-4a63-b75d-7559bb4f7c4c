import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument } from 'mongoose';

export type VNDistrictocument = HydratedDocument<VNDistrict>;

@Schema({
  versionKey: false,
  virtuals: true,
  toJSON: {
    getters: true,
    transform: function (_, ret) {
      delete ret._id;
    },
  },
})
export class VNDistrict {
  @Prop({
    type: String,
    required: true,
    index: true,
    unique: true,
  })
  code: string;

  @Prop({
    type: String,
    required: true,
  })
  name: string;

  @Prop({
    type: String,
    required: true,
    index: true,
  })
  cityCode: string;
}

export const VNDistrictSchema = SchemaFactory.createForClass(VNDistrict);
