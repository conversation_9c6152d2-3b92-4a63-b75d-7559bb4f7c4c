import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument } from 'mongoose';

export type GlobalCityDocument = HydratedDocument<GlobalCity>;

@Schema({
  versionKey: false,
  virtuals: true,
  toJSON: {
    getters: true,
    transform: function (_, ret) {
      delete ret._id;
    },
  },
})
export class GlobalCity {
  @Prop({
    type: String,
    required: true,
    index: true,
    unique: true,
  })
  code: string;

  @Prop({
    type: String,
    required: true,
  })
  name: string;

  @Prop({
    type: String,
    required: true,
    index: true,
  })
  stateCode: string;

  @Prop({
    type: Number,
    required: false,
  })
  latitude: number;

  @Prop({
    type: Number,
    required: false,
  })
  longitude: number;
}

export const GlobalCitySchema = SchemaFactory.createForClass(GlobalCity);
