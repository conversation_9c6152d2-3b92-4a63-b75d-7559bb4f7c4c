import { Controller, Get, Query } from '@nestjs/common';

import {
  ApiBadRequestResponse,
  ApiForbiddenResponse,
  ApiInternalServerErrorResponse,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiOperation,
  ApiTags,
  ApiUnauthorizedResponse,
} from '@nestjs/swagger';
import { RouteName } from '../../utils/enums';
import { getDescription, description } from '../../utils/descriptions';
import { LocationService } from './location.service';
import {
  LocationGlobalResponse,
  LocationVNResponse,
} from './dtos/response/location-reponse.dto';
import { SearchLocationVNDto } from './dtos/search-location-vn.dto';
import { SearchLocationGlobalDto } from './dtos/search.location-global.dto';

@Controller(`/${RouteName.locations}`)
@ApiTags(RouteName.locations)
@ApiOkResponse({
  description: description.httpStatus.ok,
  isArray: true,
})
@ApiForbiddenResponse({
  description: description.httpStatus.forbidden,
})
@ApiUnauthorizedResponse({ description: description.httpStatus.unAuthorized })
@ApiBadRequestResponse({ description: description.httpStatus.badRequest })
@ApiInternalServerErrorResponse({
  description: description.httpStatus.interalServer,
})
@ApiNotFoundResponse({ description: description.httpStatus.notFound })
export class LocationController {
  constructor(private readonly locationService: LocationService) {}

  @Get('vn')
  @ApiOperation({
    summary: getDescription(RouteName.locations, description.controller.gets),
  })
  async getLocationsVN(
    @Query() query: SearchLocationVNDto,
  ): Promise<LocationVNResponse[]> {
    return this.locationService.getLocationsVN(query);
  }

  @Get('global')
  @ApiOperation({
    summary: getDescription(RouteName.locations, description.controller.gets),
  })
  async gets(
    @Query() query: SearchLocationGlobalDto,
  ): Promise<LocationGlobalResponse[]> {
    return this.locationService.getLocationsGlobal(query);
  }
}
