import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

import { LocationService } from './location.service';
import { LocationController } from './location.controller';
import { VNCity, VNCitySchema } from './schemas/vn-city.schema';
import {
  GlobalCountry,
  GlobalCountrySchema,
} from './schemas/global-country.schema';
import { VNDistrict, VNDistrictSchema } from './schemas/vn-district.schema';
import { VNWard, VNWardSchema } from './schemas/vn-ward.schema';
import { GlobalCity, GlobalCitySchema } from './schemas/global-city.schema';

import { GlobalState, GlobalStateSchema } from './schemas/global-state.schema';

@Module({
  imports: [
    MongooseModule.forFeature([
      {
        name: GlobalCountry.name,
        schema: GlobalCountrySchema,
        collection: 'global-countries',
      },
    ]),
    MongooseModule.forFeature([
      {
        name: GlobalState.name,
        schema: GlobalStateSchema,
        collection: 'global-states',
      },
    ]),
    MongooseModule.forFeature([
      {
        name: GlobalCity.name,
        schema: GlobalCitySchema,
        collection: 'global-cities',
      },
    ]),
    MongooseModule.forFeature([
      { name: VNCity.name, schema: VNCitySchema, collection: 'vn-cities' },
    ]),
    MongooseModule.forFeature([
      {
        name: VNDistrict.name,
        schema: VNDistrictSchema,
        collection: 'vn-districts',
      },
    ]),
    MongooseModule.forFeature([
      { name: VNWard.name, schema: VNWardSchema, collection: 'vn-wards' },
    ]),
  ],
  controllers: [LocationController],
  providers: [LocationService],
  exports: [LocationService],
})
export class LocationModule {}
