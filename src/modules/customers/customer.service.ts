import { Model, Types } from 'mongoose';
import { Injectable } from '@nestjs/common';
import { OrderBy } from '../../utils/enums';
import { Customer } from './schemas/customer.schema';
import { InjectModel } from '@nestjs/mongoose';
import { CreateCustomerDto } from './dtos/create-customer.dto';
import { UpdateCustomerDto } from './dtos/update-customer.dto';
import { SearchCustomerDto } from './dtos/search-customer.dto';
import { MetadataResponse } from '../../utils/response/meta.response';
@Injectable()
export class CustomerService {
  constructor(
    @InjectModel(Customer.name) private customerModel: Model<Customer>,
  ) {}

  async create(payload: CreateCustomerDto): Promise<Customer> {
    const createCustomerDto = CreateCustomerDto.fromCustomer(payload);

    const customerCreated: Customer = await new this.customerModel(
      createCustomerDto,
    ).save();

    return customerCreated;
  }

  async update(payload: UpdateCustomerDto): Promise<Customer> {
    const updateCustomerDto = UpdateCustomerDto.fromCustomer(payload);
    const updated = await this.customerModel.findOneAndUpdate(
      {
        _id: payload.id,
        deletedAt: null,
      },
      {
        $set: updateCustomerDto,
      },
      { new: true }, // return old value before update set false
    );

    return updated.toJSON();
  }

  async findAndCountAll(
    search: SearchCustomerDto,
  ): Promise<MetadataResponse<Customer[]>> {
    const page = Number(search.page) || 1;
    const limit = Number(search.limit) || 10;

    const options = {
      conditions: {
        deletedAt: null,
        ...(search.customerPhone && {
          customerPhone: {
            $regex: search.customerPhone,
            $options: 'i',
          },
        }),
        ...(search.customerName && {
          customerName: {
            $regex: search.customerName,
            $options: 'i',
          },
        }),
      },
    };
    const query = await this.customerModel
      .find(options.conditions)
      .limit(limit)
      .skip((page - 1) * limit)
      .sort({ createdAt: search.order === OrderBy.asc ? 1 : -1 })
      .exec();

    const count = await this.customerModel.countDocuments(options.conditions);
    return {
      data: query,
      metadata: {
        totalRows: count,
        page: page,
        limit: limit,
        numberOfPage: Math.ceil(count / limit),
      },
    };
  }

  async delete(id: Types.ObjectId): Promise<Customer> {
    const deleted = this.customerModel.findOneAndUpdate(
      {
        _id: id,
        deletedAt: null,
      },
      {
        $set: {
          deletedAt: new Date(),
        },
      },
      { new: true }, // return old value before update set false
    );
    return deleted;
  }

  async findOne(id: Types.ObjectId): Promise<Customer> {
    return this.customerModel
      .findOne({
        _id: id,
        deletedAt: null,
      })
      .lean();
  }
}
