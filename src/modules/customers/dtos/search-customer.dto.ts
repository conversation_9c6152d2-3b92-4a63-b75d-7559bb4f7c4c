import { ApiProperty } from '@nestjs/swagger';
import { PageOptionsDto } from '../../../utils/pagination/dtos/page-option.dto';
import { description } from '../../../utils/descriptions';
import { IsOptional, IsString } from 'class-validator';

export class SearchCustomerDto extends PageOptionsDto {
  @ApiProperty({
    type: 'string',
    required: false,
    description: description.customers.validator.phone,
  })
  @IsString()
  @IsOptional()
  customerPhone: string;

  @ApiProperty({
    type: 'string',
    required: false,
    description: description.customers.validator.name,
  })
  @IsString()
  @IsOptional()
  customerName: string;

  constructor(data: Partial<SearchCustomerDto>) {
    super();
    Object.assign(this, data);
  }
}
