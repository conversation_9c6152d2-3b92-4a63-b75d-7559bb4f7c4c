import { ApiProperty } from '@nestjs/swagger';
import { IsString } from 'class-validator';
import { description } from '../../../utils/descriptions';
import { CustomerGender } from '../../../utils/enums';
export class CreateCustomerDto {
  @ApiProperty({
    type: 'string',
    required: false,
    description: description.customers.validator.name,
    default: '<PERSON><PERSON><PERSON><PERSON>',
  })
  @IsString()
  customerName: string;

  @ApiProperty({
    type: 'string',
    required: false,
    description: description.customers.validator.phone,
    default: '0905123213',
  })
  @IsString()
  customerPhone: string;

  @ApiProperty({
    type: 'string',
    required: false,
    description: description.customers.validator.address,
    default:
      'Đường 100 Xô Viết Nghệ Tĩnh, Phường 21, Quận Bình Thạnh, TP Hồ Chí Minh',
  })
  @IsString()
  customerAddress: string;

  @ApiProperty({
    type: 'string',
    required: true,
    description: description.customers.validator.gender,
    enum: Object.values(CustomerGender),
    default: CustomerGender.unknown,
  })
  @IsString()
  customerGender: string;

  @ApiProperty({
    type: 'string',
    required: false,
    description: description.cities.validator.code,
    default: '79',
  })
  @IsString()
  customerCityId: string;

  @ApiProperty({
    type: 'string',
    required: false,
    description: description.dictricts.validator.code,
    default: '765',
  })
  @IsString()
  customerDictrictId: string;

  @ApiProperty({
    type: 'string',
    required: false,
    description: description.wards.validator.code,
    default: '26953',
  })
  @IsString()
  customerWardId: string;

  constructor(data: Partial<CreateCustomerDto>) {
    Object.assign(this, data);
  }

  public static fromCustomer(payload: CreateCustomerDto): CreateCustomerDto {
    return new CreateCustomerDto({
      ...payload,
    });
  }
}
