import { toObjectId } from '../../../utils/object-id';
import { CreateCustomerDto } from './create-customer.dto';
import { Types } from 'mongoose';
import { ApiProperty } from '@nestjs/swagger';
import { IsMongoId } from 'class-validator';
import { description } from '../../../utils/descriptions';

export class UpdateCustomerDto extends CreateCustomerDto {
  @ApiProperty({
    type: 'string',
    required: false,
    description: description.products.validator.id,
  })
  @IsMongoId()
  id: Types.ObjectId;

  constructor(data: Partial<UpdateCustomerDto>) {
    super(data);
    Object.assign(this, data);
  }
  public static fromCustomer(payload: UpdateCustomerDto): UpdateCustomerDto {
    return new UpdateCustomerDto({
      id: toObjectId(payload.id),
      ...payload,
    });
  }
}
