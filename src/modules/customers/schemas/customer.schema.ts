import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument } from 'mongoose';
import { BaseSchema } from '../../shared/base/base.schema';
import { CustomerGender } from '../../../utils/enums';

export type CustomerSchema = HydratedDocument<Customer>;

@Schema({
  versionKey: false,
  virtuals: true,
  toJSON: {
    getters: true,
    transform: function (_, ret) {
      delete ret._id;
    },
  },
})
export class Customer extends BaseSchema<Customer> {
  @Prop({
    type: String,
    required: true,
  })
  customerName: string;

  @Prop({
    type: String,
    enum: Object.values(CustomerGender),
    default: CustomerGender.unknown,
    required: true,
  })
  customerGender: string;

  @Prop({
    type: String,
    required: true,
  })
  customerPhone;

  @Prop({
    type: String,
    required: true,
    default: '',
  })
  customerAddress: string;

  @Prop({
    type: String,
    required: true,
  })
  customerCityId: string;

  @Prop({
    type: String,
    required: true,
  })
  customerDictrictId: string;

  @Prop({
    type: String,
    required: true,
  })
  customerWardId: string;
}

export const CustomerSchema = SchemaFactory.createForClass(Customer);
