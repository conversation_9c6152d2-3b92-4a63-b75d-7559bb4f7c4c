import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
} from '@nestjs/common';
import { CustomerService } from './customer.service';
import { CreateCustomerDto } from './dtos/create-customer.dto';
import {
  ApiBadRequestResponse,
  ApiForbiddenResponse,
  ApiInternalServerErrorResponse,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiOperation,
  ApiParam,
  ApiTags,
  ApiUnauthorizedResponse,
} from '@nestjs/swagger';
import { RouteName } from '../../utils/enums';
import { getDescription, description } from '../../utils/descriptions';
import { SearchCustomerDto } from './dtos/search-customer.dto';
import { Customer } from './schemas/customer.schema';
import { Types } from 'mongoose';
import { UpdateCustomerDto } from './dtos/update-customer.dto';
import { MetadataResponse } from '../../utils/response/meta.response';

@Controller(`/${RouteName.customers}`)
@ApiTags(RouteName.customers)
@ApiOkResponse({
  description: description.httpStatus.ok,
  isArray: true,
})
@ApiForbiddenResponse({
  description: description.httpStatus.forbidden,
})
@ApiUnauthorizedResponse({ description: description.httpStatus.unAuthorized })
@ApiBadRequestResponse({ description: description.httpStatus.badRequest })
@ApiInternalServerErrorResponse({
  description: description.httpStatus.interalServer,
})
@ApiNotFoundResponse({ description: description.httpStatus.notFound })
export class CustomerController {
  constructor(private readonly productService: CustomerService) {}

  @Get()
  @ApiOperation({
    summary: getDescription(RouteName.customers, description.controller.gets),
  })
  async gets(
    @Query() searchCustomerDto: SearchCustomerDto,
  ): Promise<MetadataResponse<Customer[]>> {
    return this.productService.findAndCountAll(searchCustomerDto);
  }

  @Post()
  @ApiOperation({
    summary: getDescription(RouteName.customers, description.controller.post),
  })
  async create(@Body() payload: CreateCustomerDto): Promise<Customer> {
    return this.productService.create(payload);
  }

  @Put()
  @ApiOperation({
    summary: getDescription(RouteName.customers, description.controller.put),
  })
  async update(@Body() payload: UpdateCustomerDto): Promise<Customer> {
    return this.productService.update(payload);
  }

  @Get('/:id')
  @ApiParam({
    name: 'id',
    type: 'string',
    required: true,
    description: description.customers.validator.id,
  })
  @ApiOperation({
    summary: getDescription(RouteName.customers, description.controller.getOne),
  })
  async get(@Param('id') id: Types.ObjectId): Promise<Customer> {
    return this.productService.findOne(id);
  }

  @Delete('/:id')
  @ApiParam({
    name: 'id',
    type: 'string',
    required: true,
    description: description.categories.validator.id,
  })
  @ApiOperation({
    summary: getDescription(RouteName.customers, description.controller.getOne),
  })
  async delete(@Param('id') id: Types.ObjectId): Promise<Customer> {
    return this.productService.delete(id);
  }
}
