import { ApiProperty } from '@nestjs/swagger';
import { description } from '../../../utils/descriptions';
import {
  IsNotEmpty,
  IsString,
  MaxLength,
  <PERSON>Length,
  ValidateIf,
} from 'class-validator';
import { ValidatorFields } from '../../../utils/enums';

export class CreateShipFeeDto {
  @ApiProperty({
    type: 'string',
    required: true,
    description: description.shipFee.validator.name,
  })
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty({
    type: 'string',
    required: true,
    description: description.shipFee.validator.regionId,
  })
  @ValidateIf((dto) => dto.regionId)
  @IsNotEmpty()
  @IsString()
  regionId: string;

  @ApiProperty({
    type: 'string',
    required: true,
    description: description.shipFee.validator.regionName,
  })
  @IsNotEmpty()
  @IsString()
  @MaxLength(ValidatorFields.strMaxLength)
  @MinLength(ValidatorFields.strMinLength)
  regionName: string;

  @ApiProperty({
    type: 'string',
    required: true,
    description: description.shipFee.validator.regionType,
  })
  @IsNotEmpty()
  @IsString()
  regionType: string;

  // @ApiProperty({
  //   type: 'string',
  //   required: true,
  //   description: description.shipFee.validator.regionOptionId,
  // })
  // @ValidateIf((dto) => dto.regionOptionId)
  // @IsMongoId()
  // regionOptionId: string;

  // @ApiProperty({
  //   type: 'string',
  //   required: true,
  //   description: description.shipFee.validator.condition,
  // })
  // @IsString()
  // condition: string;

  // @ApiProperty({
  //   type: 'number',
  //   required: true,
  //   description: description.shipFee.validator.condition,
  // })
  // @IsNumber()
  // value: string;

  constructor(data: Partial<CreateShipFeeDto>) {
    Object.assign(this, data);
  }
}
