import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString } from 'class-validator';

export class CalculateShippingFeeDto {
  @ApiProperty({
    description: 'Country code',
    required: true,
  })
  @IsNotEmpty()
  @IsString()
  countryCode: string;

  @ApiProperty({
    description: 'City code',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  cityCode: string;

  @ApiProperty({
    description: 'District code',
    required: false,
  })
  @IsOptional()
  @IsString()
  districtCode?: string;

  @ApiProperty({
    description: 'Ward code',
    required: false,
  })
  @IsOptional()
  @IsString()
  wardCode?: string;

  @ApiProperty({
    description: 'Order value',
    required: false,
  })
  @IsOptional()
  @IsString()
  orderValue?: string;
}
