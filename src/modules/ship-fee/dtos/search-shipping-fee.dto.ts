import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString } from 'class-validator';
import { PageOptionsDto } from '../../../utils/pagination/dtos/page-option.dto';

export class SearchShippingFeeDto extends PageOptionsDto {
  @ApiProperty({
    description: 'Country code',
    required: false,
  })
  @IsOptional()
  @IsString()
  countryCode?: string;

  @ApiProperty({
    description: 'City code',
    required: false,
  })
  @IsOptional()
  @IsString()
  cityCode?: string;

  @ApiProperty({
    description: 'District code',
    required: false,
  })
  @IsOptional()
  @IsString()
  districtCode?: string;

  @ApiProperty({
    description: 'Ward code',
    required: false,
  })
  @IsOptional()
  @IsString()
  wardCode?: string;
}
