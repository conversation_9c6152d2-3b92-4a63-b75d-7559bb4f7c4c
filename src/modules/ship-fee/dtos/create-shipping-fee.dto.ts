import { ApiProperty } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  Min,
} from 'class-validator';

export class CreateShippingFeeDto {
  @ApiProperty({
    description: 'Country code',
    required: true,
  })
  @IsNotEmpty()
  @IsString()
  countryCode: string;

  @ApiProperty({
    description: 'City code',
    required: false,
  })
  @IsOptional()
  @IsString()
  cityCode?: string;

  @ApiProperty({
    description: 'District code',
    required: false,
  })
  @IsOptional()
  @IsString()
  districtCode?: string;

  @ApiProperty({
    description: 'Ward code',
    required: false,
  })
  @IsOptional()
  @IsString()
  wardCode?: string;

  @ApiProperty({
    description: 'Shipping fee amount',
    required: true,
    minimum: 0,
  })
  @IsNotEmpty()
  @IsNumber()
  @Min(0, { message: 'Fee must be greater than or equal to 0' })
  fee: number;
}
