import { IsMongoId } from 'class-validator';
import { CreateShipFeeDto } from './create-ship-fee-dto';
import { ApiProperty } from '@nestjs/swagger';
import { description } from '../../../utils/descriptions';

export class UpdateShipFeeDto extends CreateShipFeeDto {
  @ApiProperty({
    type: 'string',
    required: true,
    description: description.shipFee.validator.id,
  })
  @IsMongoId()
  id: string;

  constructor(data: Partial<UpdateShipFeeDto>) {
    super(data);
    Object.assign(this, data);
  }
}
