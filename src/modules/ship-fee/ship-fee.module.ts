import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { ShipFeeController } from './ship-fee.controller';
import { ShipFeeService } from './ship-fee.service';
import { ShippingFee, ShippingFeeSchema } from './schemas/shipping-fee.schema';
import { LocationModule } from '../locations/location.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      {
        name: ShippingFee.name,
        schema: ShippingFeeSchema,
        collection: 'shipping-fees',
      },
    ]),
    LocationModule,
  ],
  controllers: [ShipFeeController],
  providers: [ShipFeeService],
})
export class ShipFeeModule {}
