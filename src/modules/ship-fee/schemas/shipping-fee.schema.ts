import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument } from 'mongoose';
import { BaseSchema } from '../../shared/base/base.schema';
import { configSchema } from '../../../utils/schema';

export type ShippingFeeDocument = HydratedDocument<ShippingFee>;

@Schema(configSchema)
export class ShippingFee extends BaseSchema<ShippingFee> {
  @Prop({
    type: String,
    required: true,
  })
  countryCode: string;

  @Prop({
    type: String,
    required: false,
    default: null,
  })
  code: string;

  @Prop({
    type: String,
    required: false,
    default: null,
  })
  parentCode: string;

  @Prop({
    type: String,
    required: true,
    default: '',
  })
  type: string;

  @Prop({
    type: [String],
    required: false,
    default: [],
  })
  generation: string[];

  @Prop({
    type: Number,
    required: true,
    min: 0,
  })
  fee: number;
}

export const ShippingFeeSchema = SchemaFactory.createForClass(ShippingFee);
