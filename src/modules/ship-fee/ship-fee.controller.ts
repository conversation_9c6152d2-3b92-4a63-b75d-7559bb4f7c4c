import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
} from '@nestjs/common';
import {
  ApiBadRequestResponse,
  ApiCreatedResponse,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiTags,
} from '@nestjs/swagger';
import { ShipFeeService } from './ship-fee.service';
import { CreateShippingFeeDto } from './dtos/create-shipping-fee.dto';
import { UpdateShippingFeeDto } from './dtos/update-shipping-fee.dto';
import { SearchShippingFeeDto } from './dtos/search-shipping-fee.dto';
import { CalculateShippingFeeDto } from './dtos/calculate-shipping-fee.dto';
import { ShippingFee } from './schemas/shipping-fee.schema';
import { MetadataResponse } from '../../utils/response/meta.response';
import { RouteName } from '../../utils/enums';
import { ShippingFeeResponse } from './dtos/response/shipping-fee-response.dto';

@Controller(RouteName.shipFees)
@ApiTags(RouteName.shipFees)
export class ShipFeeController {
  constructor(private readonly shipFeeService: ShipFeeService) {}

  @Get('calculate')
  @ApiOperation({ summary: 'Calculate shipping fee based on location' })
  @ApiQuery({ name: 'countryId', required: true })
  @ApiQuery({ name: 'cityId', required: false })
  @ApiQuery({ name: 'districtId', required: false })
  @ApiQuery({ name: 'wardId', required: false })
  @ApiOkResponse({ description: 'Calculated shipping fee' })
  @ApiNotFoundResponse({
    description: 'No shipping fee found for this address',
  })
  async calculate(@Query() calculateDto: CalculateShippingFeeDto): Promise<{
    fee: number;
    appliedLevel: string;
    locationInfo: any;
    orderValue: number;
  }> {
    return this.shipFeeService.calculate(calculateDto);
  }

  @Post()
  @ApiOperation({ summary: 'Create a new shipping fee' })
  @ApiCreatedResponse({ description: 'Shipping fee created successfully' })
  @ApiBadRequestResponse({
    description: 'Invalid input or duplicate shipping fee',
  })
  async create(@Body() createDto: CreateShippingFeeDto): Promise<boolean> {
    return this.shipFeeService.create(createDto);
  }

  @Get()
  @ApiOperation({
    summary: 'Get all shipping fees with pagination and filters',
  })
  @ApiOkResponse({ description: 'List of shipping fees' })
  async findAll(
    @Query() searchDto: SearchShippingFeeDto,
  ): Promise<MetadataResponse<ShippingFeeResponse[]>> {
    return this.shipFeeService.findAll(searchDto);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a shipping fee by ID' })
  @ApiParam({ name: 'id', description: 'Shipping fee ID' })
  @ApiOkResponse({ description: 'Shipping fee details' })
  @ApiNotFoundResponse({ description: 'Shipping fee not found' })
  async findOne(@Param('id') id: string): Promise<ShippingFee> {
    return this.shipFeeService.findOne(id);
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update a shipping fee' })
  @ApiParam({ name: 'id', description: 'Shipping fee ID' })
  @ApiOkResponse({ description: 'Shipping fee updated successfully' })
  @ApiNotFoundResponse({ description: 'Shipping fee not found' })
  @ApiBadRequestResponse({
    description: 'Invalid input or duplicate shipping fee',
  })
  async update(
    @Param('id') id: string,
    @Body() updateDto: UpdateShippingFeeDto,
  ): Promise<ShippingFee> {
    return this.shipFeeService.update(id, updateDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a shipping fee' })
  @ApiParam({ name: 'id', description: 'Shipping fee ID' })
  @ApiOkResponse({ description: 'Shipping fee deleted successfully' })
  @ApiNotFoundResponse({ description: 'Shipping fee not found' })
  async remove(@Param('id') id: string): Promise<boolean> {
    return this.shipFeeService.remove(id);
  }
}
