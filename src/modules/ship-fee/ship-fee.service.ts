import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { ShippingFee } from './schemas/shipping-fee.schema';
import { CreateShippingFeeDto } from './dtos/create-shipping-fee.dto';
import { UpdateShippingFeeDto } from './dtos/update-shipping-fee.dto';
import { SearchShippingFeeDto } from './dtos/search-shipping-fee.dto';
import { CalculateShippingFeeDto } from './dtos/calculate-shipping-fee.dto';
import { MetadataResponse } from '../../utils/response/meta.response';
import { LocationService } from '../locations/location.service';
import { LocationVNType } from '../../utils/enums';
import { keyBy } from 'lodash';
import { ShippingFeeResponse } from './dtos/response/shipping-fee-response.dto';
import { VNCity } from '../locations/schemas/vn-city.schema';
import { VNWard } from '../locations/schemas/vn-ward.schema';
import { VNDistrict } from '../locations/schemas/vn-district.schema';
import { toObjectId } from '../../utils/object-id';

@Injectable()
export class ShipFeeService {
  constructor(
    @InjectModel(ShippingFee.name) private shippingFeeModel: Model<ShippingFee>,
    private locationService: LocationService,
  ) {}

  async create(createDto: CreateShippingFeeDto): Promise<boolean> {
    // Check for duplicate
    let isDuplicate = true;

    if (createDto.wardCode) {
      if (!createDto.districtCode || !createDto.cityCode) {
        throw new BadRequestException({
          error: 'INVALID_LOCATION',
          message: 'Missing district or city code',
        });
      }
      const createdEntity: Partial<ShippingFee> = {
        countryCode: createDto.countryCode,
        code: createDto.wardCode,
        parentCode: createDto.districtCode,
        type: LocationVNType.wards,
        fee: createDto.fee,
        generation: [
          createDto.cityCode,
          createDto.districtCode,
          createDto.wardCode,
        ],
        deletedAt: null,
      };

      const existingFee = await this.shippingFeeModel.findOne({
        code: createdEntity.code,
        type: createdEntity.type,
        deletedAt: null,
      });
      if (!existingFee) {
        await new this.shippingFeeModel(createdEntity).save();
        isDuplicate = false;
      }
    }

    if (createDto.districtCode) {
      if (!createDto.cityCode) {
        throw new BadRequestException({
          error: 'INVALID_LOCATION',
          message: 'Missing city code',
        });
      }
      const createdEntity: Partial<ShippingFee> = {
        countryCode: createDto.countryCode,
        code: createDto.districtCode,
        parentCode: createDto.cityCode,
        type: LocationVNType.districts,
        fee: createDto.fee,
        generation: [createDto.cityCode, createDto.districtCode],
        deletedAt: null,
      };

      const existingFee = await this.shippingFeeModel.findOne({
        code: createdEntity.code,
        type: createdEntity.type,
        deletedAt: null,
      });
      if (!existingFee) {
        await new this.shippingFeeModel(createdEntity).save();
        isDuplicate = false;
      }
    }
    if (createDto.cityCode) {
      const createdEntity: Partial<ShippingFee> = {
        countryCode: createDto.countryCode,
        code: createDto.cityCode,
        parentCode: null,
        type: LocationVNType.cities,
        fee: createDto.fee,
        generation: [createDto.cityCode],
        deletedAt: null,
      };

      const existingFee = await this.shippingFeeModel.findOne({
        code: createdEntity.code,
        type: createdEntity.type,
        deletedAt: null,
      });
      if (!existingFee) {
        await new this.shippingFeeModel(createdEntity).save();
        isDuplicate = false;
      }
    }
    if (isDuplicate) {
      throw new BadRequestException({
        error: 'DUPLICATE_SHIPPING_FEE',
        message: 'Phí ship cho địa chỉ này đã tồn tại',
      });
    }
    return true;
  }

  async findAll(
    searchDto: SearchShippingFeeDto,
  ): Promise<MetadataResponse<ShippingFeeResponse[]>> {
    const page = Number(searchDto.page) || 1;
    const limit = Number(searchDto.limit) || 10;
    const skip = (page - 1) * limit;

    const filter: any = { parentCode: null, deletedAt: null };

    if (searchDto.countryCode) {
      filter.countryCode = searchDto.countryCode;
    }
    if (searchDto.cityCode) {
      filter.code = searchDto.cityCode;
    }
    if (searchDto.districtCode) {
      filter.code = searchDto.districtCode;
    }
    if (searchDto.wardCode) {
      filter.code = searchDto.wardCode;
    }

    const [parentFees, total] = await Promise.all([
      this.shippingFeeModel
        .find(filter)
        .skip(skip)
        .limit(limit)
        .sort({ createdAt: -1 })
        .exec(),
      this.shippingFeeModel.countDocuments(filter),
    ]);

    const fees = await this.shippingFeeModel
      .find({
        generation: { $in: parentFees.map((fee) => fee.code) },
        deletedAt: null,
      })
      .exec();

    const shippingFeeResponse = fees.map(
      (fee) => new ShippingFeeResponse(fee.toJSON()),
    );

    const cityCodes = fees
      .filter((fee) => fee.type === LocationVNType.cities)
      .map((fee) => fee.code);

    const districtCodes = fees
      .filter((fee) => fee.type === LocationVNType.districts)
      .map((fee) => fee.code);

    const wardCodes = fees
      .filter((fee) => fee.type === LocationVNType.wards)
      .map((fee) => fee.code);

    if (cityCodes.length > 0) {
      const cities = (await this.locationService.getLocationsVNByCodes(
        LocationVNType.cities,
        cityCodes,
      )) as unknown as VNCity[];

      const keyByCities = keyBy(cities, 'code');

      shippingFeeResponse
        .filter((fee) => fee.type === LocationVNType.cities)
        .forEach((fee: ShippingFeeResponse) => {
          fee.name = fee.code ? keyByCities[fee.code].name : null;
        });
    }

    if (districtCodes.length > 0) {
      const districts = (await this.locationService.getLocationsVNByCodes(
        LocationVNType.districts,
        districtCodes,
      )) as unknown as VNDistrict[];

      const keyByDistricts = keyBy(districts, 'code');

      shippingFeeResponse
        .filter((fee) => fee.type === LocationVNType.districts)
        .forEach((fee: ShippingFeeResponse) => {
          fee.name = fee.code ? keyByDistricts[fee.code].name : null;
        });
    }

    if (wardCodes.length > 0) {
      const wards = (await this.locationService.getLocationsVNByCodes(
        LocationVNType.wards,
        wardCodes,
      )) as unknown as VNWard[];

      const keyByWards = keyBy(wards, 'code');

      shippingFeeResponse
        .filter((fee) => fee.type === LocationVNType.wards)
        .forEach((fee: ShippingFeeResponse) => {
          fee.name = fee.code ? keyByWards[fee.code].name : null;
        });
    }

    const groupedCities = this.recursionShippingFee(shippingFeeResponse, null);
    return {
      data: groupedCities,
      metadata: {
        totalRows: total,
        page: page,
        limit: limit,
        numberOfPage: Math.ceil(total / limit),
      },
    };
  }

  recursionShippingFee(
    shippingFees: ShippingFeeResponse[],
    parentCode: string,
  ): ShippingFeeResponse[] {
    const parentShippingFees = shippingFees.filter(
      (fee) => fee.parentCode === parentCode,
    );

    if (parentShippingFees.length) {
      for (const shipping of parentShippingFees) {
        const childs = this.recursionShippingFee(shippingFees, shipping.code);
        shipping.groups = childs;
      }
    }

    return parentShippingFees;
  }

  async findOne(id: string): Promise<ShippingFee> {
    const fee = await this.shippingFeeModel
      .findOne({ _id: id, deletedAt: null })
      .exec();

    if (!fee) {
      throw new NotFoundException({
        error: 'SHIPPING_FEE_NOT_FOUND',
        message: 'Shipping fee not found',
      });
    }

    return fee;
  }

  async update(
    id: string,
    updateDto: UpdateShippingFeeDto,
  ): Promise<ShippingFee> {
    // Validate fee if provided
    if (updateDto.fee !== undefined && updateDto.fee <= 0) {
      throw new BadRequestException({
        error: 'INVALID_FEE',
        message: 'fee must be greater than 0',
      });
    }

    // Check if exists
    const existingFee = await this.shippingFeeModel.findOne({
      _id: toObjectId(id),
      deletedAt: null,
    });

    if (!existingFee) {
      throw new NotFoundException({
        error: 'SHIPPING_FEE_NOT_FOUND',
        message: 'Shipping fee not found',
      });
    }
    existingFee.fee = updateDto.fee;
    existingFee.updatedAt = new Date();
    await existingFee.save();

    return existingFee;
  }

  async remove(id: string): Promise<boolean> {
    const fee = await this.shippingFeeModel.findOne({
      _id: id,
      deletedAt: null,
    });

    if (!fee) {
      throw new NotFoundException({
        error: 'SHIPPING_FEE_NOT_FOUND',
        message: 'Shipping fee not found',
      });
    }

    await this.shippingFeeModel
      .deleteMany({
        generation: {
          $in: fee.code,
        },
      })
      .exec();
    return true;
  }

  async calculate(calculateDto: CalculateShippingFeeDto): Promise<{
    fee: number;
    appliedLevel: string;
    locationInfo: any;
    orderValue: number;
  }> {
    const { cityCode, districtCode, wardCode } = calculateDto;
    const defaultOrderLevel = [
      LocationVNType.wards,
      LocationVNType.districts,
      LocationVNType.cities,
    ];
    const shipFees = await this.shippingFeeModel
      .find({
        code: [cityCode, districtCode, wardCode].map((item) => item),
        deletedAt: null,
      })
      .exec();

    for (const item of defaultOrderLevel) {
      const fee = shipFees.find((fee) => fee.type === item);
      if (fee) {
        const location = {
          cityCode: fee.generation[0],
          city: null,
          districtCode: fee.generation[1],
          district: null,
          wardCode: fee.generation[2],
          ward: null,
        };

        if (location.cityCode) {
          const [city] = await this.locationService.getLocationsVNByCodes(
            LocationVNType.cities,
            [location.cityCode],
          );
          location.city = city;
        }

        if (location.districtCode) {
          const [district] = await this.locationService.getLocationsVNByCodes(
            LocationVNType.districts,
            [location.districtCode],
          );
          location.district = district;
        }

        if (location.wardCode) {
          const [ward] = await this.locationService.getLocationsVNByCodes(
            LocationVNType.wards,
            [location.wardCode],
          );
          location.ward = ward;
        }
        return {
          fee: fee.fee,
          appliedLevel: fee.type,
          locationInfo: location,
          orderValue: Number(calculateDto.orderValue),
        };
      }
    }

    // If no shipping fee found
    throw new NotFoundException({
      error: 'SHIPPING_FEE_NOT_FOUND',
      message: 'No shipping fee found for this address',
    });
  }
}
