import { Expose } from 'class-transformer';
import { Application } from '../schemas/application-schema';

export class ApplicationResponseDto {
  @Expose()
  id: string;

  @Expose()
  applicationName: string;

  @Expose()
  clientId: string;

  @Expose()
  clientSecret: string;

  @Expose()
  grantType: string;

  @Expose()
  authUrl: string;

  @Expose()
  tokenUrl: string;

  @Expose()
  responseType: string;

  @Expose()
  redirectUrl: string;

  @Expose()
  scope: string;

  @Expose()
  applicationType: string;

  @Expose()
  status: string;

  @Expose()
  active: boolean;

  @Expose()
  state: string;

  @Expose()
  accessToken: string;

  @Expose()
  expireIn: number;

  @Expose()
  verify: boolean;

  @Expose()
  createdAt: Date;

  @Expose()
  updatedAt: Date;

  constructor(partial: Partial<Application>) {
    Object.assign(this, partial);
  }
}

export class ApplicationEmbedResponseDto {
  @Expose()
  socialName: string;

  @Expose()
  url: string;

  constructor(partial: Partial<ApplicationEmbedResponseDto>) {
    Object.assign(this, partial);
  }
}
