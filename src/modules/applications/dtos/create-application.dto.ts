import { ApiProperty } from '@nestjs/swagger';
import {
  IsBoolean,
  IsNotEmpty,
  IsString,
  MaxLength,
  <PERSON><PERSON>ength,
} from 'class-validator';
import { CommonStatus, ValidatorFields } from '../../../utils/enums';
import { description } from '../../../utils/descriptions';

export class CreateApplicationDto {
  @ApiProperty({
    type: 'string',
    required: true,
    description: description.applications.validator.name,
  })
  @IsNotEmpty()
  @IsString()
  @MaxLength(ValidatorFields.strMaxLength)
  @MinLength(ValidatorFields.strMinLength)
  applicationName: string;

  @ApiProperty({
    type: 'string',
    required: true,
    description: description.applications.validator.clientId,
  })
  @IsNotEmpty()
  @IsString()
  clientId: string;

  @ApiProperty({
    type: 'string',
    required: true,
    description: description.applications.validator.clientSecret,
  })
  @IsNotEmpty()
  @IsString()
  clientSecret: string;

  @ApiProperty({
    type: 'string',
    required: true,
    description: description.applications.validator.grantType,
  })
  @IsNotEmpty()
  @IsString()
  grantType: string;

  @ApiProperty({
    type: 'string',
    required: true,
    description: description.applications.validator.authUrl,
  })
  @IsNotEmpty()
  @IsString()
  authUrl: string;

  @ApiProperty({
    type: 'string',
    required: true,
    description: description.applications.validator.tokenUrl,
  })
  @IsNotEmpty()
  @IsString()
  tokenUrl: string;

  @ApiProperty({
    type: 'string',
    required: true,
    description: description.applications.validator.responseType,
  })
  @IsNotEmpty()
  @IsString()
  responseType: string;

  @ApiProperty({
    type: 'string',
    required: true,
    description: description.applications.validator.redirectUrl,
  })
  @IsNotEmpty()
  @IsString()
  redirectUrl: string;

  @ApiProperty({
    type: 'string',
    required: true,
    description: description.applications.validator.scope,
  })
  @IsNotEmpty()
  @IsString()
  scope: string;

  @ApiProperty({
    type: 'string',
    required: true,
    description: description.applications.validator.applicationType,
  })
  @IsNotEmpty()
  @IsString()
  applicationType: string;

  @ApiProperty({
    enum: Object.values(CommonStatus),
    default: CommonStatus.active,
    description: description.common.status,
    required: true,
  })
  @IsString()
  status: string;

  @ApiProperty({
    type: 'boolean',
    required: true,
    description: description.applications.validator.active,
  })
  @IsBoolean()
  active: boolean;

  constructor(data: Partial<CreateApplicationDto>) {
    Object.assign(this, data);
  }
}
