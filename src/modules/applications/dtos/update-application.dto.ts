import { IsMongoId } from 'class-validator';
import { CreateApplicationDto } from './create-application.dto';
import { ApiProperty } from '@nestjs/swagger';
import { description } from '../../../utils/descriptions';
import { Types } from 'mongoose';

export class UpdateApplicationDto extends CreateApplicationDto {
  @ApiProperty({
    type: 'string',
    required: true,
    description: description.attributes.validator.id,
  })
  @IsMongoId()
  id: Types.ObjectId;
}
