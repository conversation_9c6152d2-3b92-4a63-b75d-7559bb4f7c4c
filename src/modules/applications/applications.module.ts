import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { Application, ApplicationSchema } from './schemas/application-schema';
import { ApplicationController } from './applications.controller';
import { ApplicationService } from './applications.service';
import { InstagramApiService } from '../socials/instagram/instagram-api.service';
import { SocialModule } from '../socials/social.module';
@Module({
  imports: [
    MongooseModule.forFeature([
      {
        name: Application.name,
        schema: ApplicationSchema,
        collection: 'applications',
      },
    ]),
    SocialModule,
  ],
  controllers: [ApplicationController],
  providers: [ApplicationService, InstagramApiService],
  exports: [ApplicationService],
})
export class ApplicationModule {}
