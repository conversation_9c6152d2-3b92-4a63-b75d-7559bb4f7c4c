import {
  ApplicationEmbedResponseDto,
  ApplicationResponseDto,
} from './dtos/response.dto';
import { Model, Types } from 'mongoose';
import { Injectable, NotFoundException } from '@nestjs/common';
import { CreateApplicationDto } from './dtos/create-application.dto';
import { UpdateApplicationDto } from './dtos/update-application.dto';
import { InjectModel } from '@nestjs/mongoose';
import { Application } from './schemas/application-schema';
import { MetadataResponse } from '../../utils/response/meta.response';
import { ApplicationType } from '../../utils/enums';
import { toObjectId } from '../../utils/object-id';
import { InstagramApiService } from '../socials/instagram/instagram-api.service';
import { OAuth2AccessTokenType, OAuth2EmbedType } from '../socials/types';
import { generateState } from '../../utils/crypto.util';
import { SocialService } from '../socials/social.service';

@Injectable()
export class ApplicationService {
  constructor(
    @InjectModel(Application.name)
    private applicationModel: Model<Application>,
    private instagramApiService: InstagramApiService,
    private socialService: SocialService,
  ) {}

  async getLinkEmbed(id: string): Promise<ApplicationEmbedResponseDto> {
    const query = await this.applicationModel
      .findOne({
        _id: toObjectId(id),
        deletedAt: null,
      })
      .exec();

    if (!query) {
      throw new NotFoundException();
    }

    const state = generateState();
    switch (query.applicationType) {
      case ApplicationType.instagram: {
        const urlObject: OAuth2EmbedType = {
          authUrl: query.authUrl,
          clientId: query.clientId,
          redirectUrl: query.redirectUrl,
          responseType: query.responseType,
          scope: query.scope,
        };
        // Dont use await
        await this.applicationModel.updateOne(
          {
            _id: id,
          },
          {
            $set: {
              state,
            },
          },
        );

        return {
          socialName: ApplicationType.instagram,
          url: this.instagramApiService.getEmbedLink(urlObject, state),
        };
      }
      default:
        throw new NotFoundException('Unsupported application type');
    }
  }

  async findAndCountAll(): Promise<MetadataResponse<ApplicationResponseDto[]>> {
    const conditions = {
      deletedAt: null,
    };

    const query = await this.applicationModel.find(conditions).exec();

    const count = await this.applicationModel.countDocuments(conditions);

    return {
      data: query.map((app) => new ApplicationResponseDto(app.toJSON())),
      metadata: {
        totalRows: count,
        page: 1,
        limit: 10,
        numberOfPage: 1,
      },
    };
  }

  async findOne(id: Types.ObjectId): Promise<Application> {
    return this.applicationModel.findById(id).exec();
  }

  async create(dto: CreateApplicationDto): Promise<Application> {
    return this.applicationModel.create(dto);
  }

  async update(dto: UpdateApplicationDto): Promise<Application> {
    return this.applicationModel.findOneAndUpdate(
      {
        _id: dto.id,
        deletedAt: null,
      },
      {
        $set: dto,
      },
      { new: true }, // return old value before update set false
    );
  }

  async delete(id: Types.ObjectId): Promise<Application> {
    const deleted = this.applicationModel.findOneAndUpdate(
      {
        _id: id,
        deletedAt: null,
      },
      {
        $set: {
          deletedAt: new Date(),
        },
      },
      { new: true }, // return old value before update set false
    );
    return deleted;
  }

  async verifySocial(code: string, state: string): Promise<boolean> {
    try {
      const application = await this.applicationModel
        .findOne({
          deletedAt: null,
          state: state,
        })
        .exec();

      if (!application) {
        throw new NotFoundException();
      }
      const urlObject: OAuth2AccessTokenType = {
        clientId: application.clientId,
        clientSecret: application.clientSecret,
        redirectUrl: application.redirectUrl,
        responseType: application.responseType,
        tokenUrl: application.tokenUrl,
        scope: application.scope,
        grantType: application.grantType,
        code: code,
      };
      const shortAccessToken = await this.instagramApiService.getAccessToken(
        urlObject,
      );
      if (!shortAccessToken) return false;

      const longAccessToken =
        await this.instagramApiService.getLongLivedAccessToken({
          shortLivedToken: shortAccessToken,
          clientSecret: application.clientSecret,
        });

      if (!longAccessToken) return false;

      await this.applicationModel.updateOne(
        {
          _id: application._id,
        },
        {
          $set: {
            accessToken: longAccessToken.accessToken,
            expireIn: longAccessToken.expireIn,
            verify: true,
          },
        },
      );
      return true;
    } catch (error) {
      return false;
    }
  }

  async refreshToken(appId: string) {
    const application = await this.applicationModel.findOne({
      deletedAt: null,
      _id: new Types.ObjectId(appId),
    });

    if (!application) {
      throw new NotFoundException();
    }

    const refreshToken = await this.instagramApiService.refreshAccessToken(
      application.accessToken,
    );
    await this.applicationModel.updateOne(
      {
        _id: application._id,
      },
      {
        $set: {
          accessToken: refreshToken.accessToken,
          expireIn: refreshToken.expireIn,
          verify: true,
        },
      },
    );
    return refreshToken;
  }

  async getImageSocial(id: string) {
    const instagram = await this.applicationModel
      .findOne({
        deletedAt: null,
        _id: toObjectId(id),
      })
      .exec();
    if (!instagram) {
      throw new NotFoundException();
    }
    const images = await this.socialService.getImageInstagram(
      instagram.accessToken,
    );
    await this.socialService.saveImageInstagram(id, images);

    return images;
  }
}
