import { <PERSON>p, Schem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument } from 'mongoose';
import { configSchema } from '../../../utils/schema';
import { ApplicationType, CommonStatus } from '../../../utils/enums';
import { BaseSchema } from '../../shared/base/base.schema';

export type ApplicationDocument = HydratedDocument<Application>;

@Schema(configSchema)
export class Application extends BaseSchema<Application> {
  @Prop({
    type: String,
    required: false,
  })
  applicationName: string;

  @Prop({
    type: String,
    required: false,
  })
  clientId: string;

  @Prop({
    type: String,
    required: false,
  })
  clientSecret: string;

  @Prop({
    type: String,
    required: false,
  })
  grantType: string;

  @Prop({
    type: String,
    required: false,
  })
  authUrl: string;

  @Prop({
    type: String,
    required: false,
  })
  tokenUrl: string;

  @Prop({
    type: String,
    required: false,
  })
  responseType: string;

  @Prop({
    type: String,
    required: false,
  })
  redirectUrl: string;

  @Prop({
    type: String,
    required: false,
  })
  scope: string;

  @Prop({
    type: String,
    enum: ApplicationType,
    required: false,
  })
  applicationType: string;

  @Prop({
    type: String,
    enum: Object.values(CommonStatus),
    default: CommonStatus.active,
    required: true,
  })
  status: string;

  @Prop({
    type: Boolean,
    required: false,
  })
  active: boolean;

  @Prop({
    type: String,
    required: false,
  })
  state: string;

  @Prop({
    type: String,
    required: false,
  })
  accessToken: string;

  @Prop({
    type: Number,
    required: false,
  })
  expireIn: number;

  @Prop({
    type: Boolean,
    required: false,
    default: false,
  })
  verify: boolean;
}

export const ApplicationSchema = SchemaFactory.createForClass(Application);
