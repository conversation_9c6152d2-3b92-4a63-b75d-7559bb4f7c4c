import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
} from '@nestjs/common';
import { ApplicationService } from './applications.service';
import { CreateApplicationDto } from './dtos/create-application.dto';
import {
  ApiBadRequestResponse,
  ApiForbiddenResponse,
  ApiInternalServerErrorResponse,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiOperation,
  ApiParam,
  ApiTags,
  ApiUnauthorizedResponse,
} from '@nestjs/swagger';
import { RouteName } from '../../utils/enums';
import { getDescription, description } from '../../utils/descriptions';
import { UpdateApplicationDto } from './dtos/update-application.dto';
import { Types } from 'mongoose';
import { Application } from './schemas/application-schema';
import { MetadataResponse } from '../../utils/response/meta.response';
import {
  ApplicationEmbedResponseDto,
  ApplicationResponseDto,
} from './dtos/response.dto';

@Controller(`/${RouteName.applications}`)
@ApiTags(RouteName.applications)
@ApiOkResponse({
  description: description.httpStatus.ok,
  isArray: true,
})
@ApiForbiddenResponse({
  description: description.httpStatus.forbidden,
})
@ApiUnauthorizedResponse({ description: description.httpStatus.unAuthorized })
@ApiBadRequestResponse({ description: description.httpStatus.badRequest })
@ApiInternalServerErrorResponse({
  description: description.httpStatus.interalServer,
})
@ApiNotFoundResponse({ description: description.httpStatus.notFound })
export class ApplicationController {
  constructor(private readonly applicationService: ApplicationService) {}

  @Get(':id/social-image')
  @ApiOperation({
    summary: getDescription(
      RouteName.applications,
      description.controller.gets,
    ),
  })
  @ApiParam({
    name: 'id',
    type: 'string',
    required: true,
    description: description.applications.validator.name,
  })
  async getImageSocial(@Param('id') id: string): Promise<any> {
    const result = await this.applicationService.getImageSocial(id);
    return result;
  }

  @Get()
  @ApiOperation({
    summary: getDescription(
      RouteName.applications,
      description.controller.gets,
    ),
  })
  async gets(): Promise<MetadataResponse<ApplicationResponseDto[]>> {
    return this.applicationService.findAndCountAll();
  }

  // @Get('refresh-token')
  // @Public()
  // @ApiOperation({
  //   summary: getDescription(
  //     RouteName.applications,
  //     description.controller.gets,
  //   ),
  // })
  // async refreshToken(
  //   @Query() query: { appId: string },
  // ): Promise<MetadataResponse<Application[]>> {
  //   return this.applicationService.refreshToken(query.appId);
  // }

  @Get('link/:id')
  @ApiOperation({
    summary: getDescription(
      RouteName.applications,
      description.controller.gets,
    ),
  })
  @ApiParam({
    name: 'id',
    type: 'string',
    required: true,
    description: description.applications.validator.name,
  })
  async getLinkEmbed(
    @Param('id') id: string,
  ): Promise<ApplicationEmbedResponseDto> {
    return this.applicationService.getLinkEmbed(id);
  }

  @Get('/:id')
  @ApiParam({
    name: 'id',
    type: 'string',
    required: true,
    description: description.applications.validator.id,
  })
  @ApiOperation({
    summary: getDescription(
      RouteName.applications,
      description.controller.getOne,
    ),
  })
  async get(@Param('id') id: Types.ObjectId): Promise<Application> {
    return this.applicationService.findOne(id);
  }

  @Post()
  @ApiOperation({
    summary: getDescription(
      RouteName.applications,
      description.controller.post,
    ),
  })
  async create(@Body() payload: CreateApplicationDto): Promise<Application> {
    return this.applicationService.create(payload);
  }

  @Put()
  @ApiOperation({
    summary: getDescription(RouteName.applications, description.controller.put),
  })
  async update(@Body() payload: UpdateApplicationDto): Promise<Application> {
    return this.applicationService.update(payload);
  }

  @Delete('/:id')
  @ApiParam({
    name: 'id',
    type: 'string',
    required: true,
    description: description.applications.validator.id,
  })
  @ApiOperation({
    summary: getDescription(
      RouteName.applications,
      description.controller.getOne,
    ),
  })
  async delete(@Param('id') id: Types.ObjectId): Promise<Application> {
    return this.applicationService.delete(id);
  }
}
