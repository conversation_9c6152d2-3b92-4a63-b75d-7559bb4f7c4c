import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  Put,
  Query,
  UseInterceptors,
} from '@nestjs/common';
import { BannerSectionService } from './banner-section.service';
import { CreateBannerSectionDto } from './dtos/create-banner-section.dto';
import {
  ApiBadRequestResponse,
  ApiForbiddenResponse,
  ApiInternalServerErrorResponse,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiOperation,
  ApiParam,
  ApiTags,
  ApiUnauthorizedResponse,
} from '@nestjs/swagger';
import { RouteName } from '../../utils/enums';
import { getDescription, description } from '../../utils/descriptions';
import { Types } from 'mongoose';
import { PageOptionsDto } from '../../utils/pagination/dtos/page-option.dto';
import { ResponseMessage } from '../../decorators/responses/message.decorator';
import { SerializeResponse } from '../../decorators/responses/serialize.decorator';
import { SerializerInterceptor } from '../../interceptors/serialize.interceptor';
import { BannerSectionResponse } from './responses/banner.response';
import { UpdateBannerSectionDto } from './dtos/update-banner-section.dto';
import { MetadataResponse } from '../../utils/response/meta.response';
import { BannerSection } from './schemas/banner.schema';

@Controller(`/${RouteName.bannerSections}`)
@ApiTags(RouteName.bannerSections)
@ApiOkResponse({
  description: description.httpStatus.ok,
  isArray: true,
})
@ApiForbiddenResponse({
  description: description.httpStatus.forbidden,
})
@ApiUnauthorizedResponse({ description: description.httpStatus.unAuthorized })
@ApiBadRequestResponse({ description: description.httpStatus.badRequest })
@ApiInternalServerErrorResponse({
  description: description.httpStatus.interalServer,
})
@ApiNotFoundResponse({ description: description.httpStatus.notFound })
export class BannerController {
  constructor(private readonly bannerSectionService: BannerSectionService) {}

  @Get()
  @SerializeResponse(BannerSectionResponse)
  @ResponseMessage('Thành công')
  @ApiOperation({
    summary: getDescription(
      RouteName.bannerSections,
      description.controller.gets,
    ),
  })
  @UseInterceptors(SerializerInterceptor)
  async gets(
    @Query() pagination: PageOptionsDto,
  ): Promise<MetadataResponse<BannerSection[]>> {
    return this.bannerSectionService.findAll(pagination);
  }

  @Get('/:id')
  @ApiParam({
    name: 'id',
    type: 'string',
    required: true,
    description: description.bannerSections.validator.id,
  })
  @ApiOperation({
    summary: getDescription(
      RouteName.bannerSections,
      description.controller.getOne,
    ),
  })
  async get(@Param('id') id: Types.ObjectId): Promise<BannerSectionResponse> {
    return this.bannerSectionService.findOne(id);
  }

  @Post()
  @ApiOperation({
    summary: getDescription(
      RouteName.bannerSections,
      description.controller.post,
    ),
  })
  async create(
    @Body() payload: CreateBannerSectionDto,
  ): Promise<BannerSection> {
    return this.bannerSectionService.create(payload);
  }

  @Put()
  @ApiOperation({
    summary: getDescription(
      RouteName.bannerSections,
      description.controller.put,
    ),
  })
  async update(
    @Body() payload: UpdateBannerSectionDto,
  ): Promise<BannerSection> {
    return this.bannerSectionService.update(payload);
  }
}
