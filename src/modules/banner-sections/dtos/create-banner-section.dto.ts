import { ApiProperty } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsNumber,
  IsString,
  MaxLength,
  <PERSON><PERSON>ength,
  ValidateNested,
} from 'class-validator';
import {
  BannerSectionType,
  CommonStatus,
  ValidatorFields,
} from '../../../utils/enums';
import { description } from '../../../utils/descriptions';
import { CreateBannerImageDto } from './create-banner-image.dto';
import { Type } from 'class-transformer';

export class CreateBannerSectionDto {
  @ApiProperty({
    type: 'string',
    required: true,
    description: description.bannerSections.validator.name,
  })
  @IsNotEmpty()
  @IsString()
  @MaxLength(ValidatorFields.strMaxLength)
  @MinLength(ValidatorFields.strMinLength)
  bannerName: string;

  @ApiProperty({
    type: 'string',
    required: true,
    description: description.bannerSections.validator.type,
    enum: Object.values(BannerSectionType),
  })
  @IsString()
  bannerType: string;

  @ApiProperty({
    type: 'number',
    required: true,
    default: 0,
    description: description.bannerSections.validator.max,
  })
  @IsNumber()
  max: number;

  @ApiProperty({
    enum: Object.values(CommonStatus),
    default: CommonStatus.active,
    description: description.common.status,
    required: true,
  })
  @IsString()
  status: string;

  @ApiProperty({ type: CreateBannerImageDto, isArray: true })
  @ValidateNested({ each: true })
  @Type(() => CreateBannerImageDto)
  bannerImages: CreateBannerImageDto[];

  @ApiProperty({
    type: 'string',
    required: true,
    description: description.bannerSections.validator.url,
  })
  @IsString()
  bannerDefaultUrl: string;

  constructor(data: Partial<CreateBannerSectionDto>) {
    Object.assign(this, data);
  }
}
