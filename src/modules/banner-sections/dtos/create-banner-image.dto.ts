import { ApiProperty } from '@nestjs/swagger';
import {
  IsMongoId,
  IsNotEmpty,
  IsString,
  MaxLength,
  MinLength,
  ValidateIf,
} from 'class-validator';
import { BannerSectionType, ValidatorFields } from '../../../utils/enums';
import { description } from '../../../utils/descriptions';

export class CreateBannerImageDto {
  @ApiProperty({
    type: 'string',
    required: true,
    description: description.bannerImages.validator.name,
  })
  @IsMongoId()
  @IsNotEmpty()
  id: string;

  @ApiProperty({
    type: 'string',
    required: true,
    description: description.bannerImages.validator.name,
  })
  @IsString()
  @MaxLength(ValidatorFields.strMaxLength)
  @MinLength(ValidatorFields.strMinLength)
  bannerPosition: string;

  @ApiProperty({
    type: 'string',
    required: true,
    description: description.bannerImages.validator.type,
    enum: Object.values(BannerSectionType),
  })
  @IsString()
  image: string;

  @ApiProperty({
    type: 'string',
    required: true,
    description: description.bannerImages.validator.type,
    enum: Object.values(BannerSectionType),
  })
  @IsString()
  @ValidateIf((field) => field.markupText)
  directLink: string;

  @ApiProperty({
    type: 'string',
    required: true,
    description: description.bannerImages.validator.type,
    enum: Object.values(BannerSectionType),
  })
  @IsString()
  @ValidateIf((field) => field.markupText)
  heading: string;

  @ApiProperty({
    type: 'string',
    required: true,
    description: description.bannerImages.validator.subHeading,
  })
  @IsString()
  @ValidateIf((field) => field.markupText)
  subHeading: string;

  @ApiProperty({
    type: 'string',
    required: true,
    description: description.bannerImages.validator.subHeading2,
  })
  @IsString()
  @ValidateIf((field) => field.markupText)
  subHeading2: string;

  @ApiProperty({
    type: 'string',
    required: true,
    description: description.bannerImages.validator.subHeading3,
  })
  @IsString()
  @ValidateIf((field) => field.markupText)
  subHeading3: string;

  @ApiProperty({
    type: 'string',
    required: true,
    description: description.bannerImages.validator.markupText,
  })
  @IsString()
  @ValidateIf((field) => field.markupText)
  markupText: string;

  constructor(data: Partial<CreateBannerImageDto>) {
    Object.assign(this, data);
  }
}
