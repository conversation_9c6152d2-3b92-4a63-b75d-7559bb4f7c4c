import { ApiProperty } from '@nestjs/swagger';
import { CreateBannerSectionDto } from './create-banner-section.dto';
import { description } from '../../../utils/descriptions';
import { IsMongoId } from 'class-validator';
export class UpdateBannerSectionDto extends CreateBannerSectionDto {
  @ApiProperty({
    type: 'string',
    required: true,
    description: description.categories.validator.id,
  })
  @IsMongoId()
  id: string;
}
