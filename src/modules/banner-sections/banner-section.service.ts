import { Model, Types } from 'mongoose';
import { Injectable } from '@nestjs/common';
import { CreateBannerSectionDto } from './dtos/create-banner-section.dto';
import { UpdateBannerSectionDto } from './dtos/update-banner-section.dto';
import { BannerSection } from './schemas/banner.schema';
import { InjectModel } from '@nestjs/mongoose';
import { PageOptionsDto } from '../../utils/pagination/dtos/page-option.dto';
import { BannerImage } from '../banners-images/schemas/banner-image.schema';
import { toObjectId } from '../../utils/object-id';
import { BannerSectionResponse } from './responses/banner.response';
import { plainToInstance } from 'class-transformer';
import { keyBy } from 'lodash';
import { MetadataResponse } from '../../utils/response/meta.response';

@Injectable()
export class BannerSectionService {
  constructor(
    @InjectModel(BannerSection.name) private banner: Model<BannerSection>,
    @InjectModel(BannerImage.name) private bannerImage: Model<BannerImage>,
  ) {}

  async findAll(
    pagination: PageOptionsDto,
  ): Promise<MetadataResponse<BannerSection[]>> {
    const page = Number(pagination.page) || 1;
    const limit = Number(pagination.limit) || 10;

    const query = await this.banner
      .find({
        deletedAt: {
          $eq: null,
        },
      })
      .limit(limit)
      .skip((page - 1) * limit)
      .exec();

    const queryTotal = await this.banner
      .find({
        deletedAt: {
          // $not: {
          //   $eq: null,
          // },
          $eq: null,
        },
      })
      .exec();
    return {
      data: query,
      metadata: {
        totalRows: queryTotal.length,
        page: page,
        limit: limit,
      },
    };
  }

  async findOne(id: Types.ObjectId): Promise<BannerSectionResponse> {
    const banner = await this.banner.findById(id).exec();

    const bannerImages = await this.bannerImage.find({
      bannerSectionId: banner._id.toString(),
    });
    return plainToInstance(BannerSectionResponse, {
      ...banner.toJSON(),
      bannerImages: keyBy(bannerImages, 'id'),
    });
  }

  async findOneByType(type: string): Promise<BannerSection> {
    return this.banner.findOne({
      bannerType: type,
    });
  }

  async create(createCatDto: CreateBannerSectionDto): Promise<BannerSection> {
    const created = new this.banner(createCatDto);
    return created.save();
  }

  async update(payload: UpdateBannerSectionDto): Promise<BannerSection> {
    const updated = this.banner.findOneAndUpdate(
      {
        _id: payload.id,
        deletedAt: null,
      },
      {
        $set: payload,
      },
      { new: true }, // return old value before update set false
    );
    if (payload.bannerImages) {
      this.bannerImage.bulkWrite(
        payload.bannerImages.map((item) => ({
          updateOne: {
            filter: { _id: toObjectId(item.id) },
            update: {
              $set: {
                ...item,
                bannerSectionId: payload.id,
                _id: toObjectId(item.id),
              },
            },
            upsert: true,
          },
        })),
      );
    }
    return updated;
  }
}
