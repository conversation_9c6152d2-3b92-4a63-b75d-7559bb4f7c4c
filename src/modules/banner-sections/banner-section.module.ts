import { Modu<PERSON> } from '@nestjs/common';
import { BannerController } from './banner-section.controller';
import { MongooseModule } from '@nestjs/mongoose';
import { BannerSection, BannerSectionSchema } from './schemas/banner.schema';
import { BannerSectionService } from './banner-section.service';
import { BannerSectionLoaderService } from './banner-section-loader.service';
import {
  BannerImage,
  BannerImageSchema,
} from '../banners-images/schemas/banner-image.schema';

@Module({
  imports: [
    MongooseModule.forFeature([
      {
        name: BannerSection.name,
        schema: BannerSectionSchema,
        collection: 'banner-sections',
      },
      {
        name: BannerImage.name,
        schema: BannerImageSchema,
        collection: 'banner-images',
      },
    ]),
  ],
  controllers: [BannerController],
  providers: [BannerSectionService, BannerSectionLoaderService],
})
export class BannerSectionModule {}
