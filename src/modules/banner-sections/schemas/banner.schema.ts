import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument } from 'mongoose';
import { BannerSectionType, CommonStatus } from '../../../utils/enums';
import { BaseSchema } from '../../shared/base/base.schema';

export type BannerSectionDocument = HydratedDocument<BannerSection>;

@Schema({
  versionKey: false,
  toJSON: {
    getters: true,
  },
})
export class BannerSection extends BaseSchema<BannerSection> {
  @Prop({
    type: String,
    required: true,
  })
  bannerName: string;

  @Prop({
    type: Number,
    required: true,
  })
  max: number;

  @Prop({
    type: String,
    enum: Object.values(BannerSectionType),
    required: true,
  })
  bannerType: string;

  @Prop({
    type: String,
    enum: Object.values(CommonStatus),
    default: CommonStatus.active,
    required: true,
  })
  status: string;

  @Prop({
    type: String,
    required: false,
    default: '',
  })
  bannerDefaultUrl: string;
}

export const BannerSectionSchema = SchemaFactory.createForClass(BannerSection);
