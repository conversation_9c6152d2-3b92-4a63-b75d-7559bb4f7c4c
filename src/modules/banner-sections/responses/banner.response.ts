import { Expose } from 'class-transformer';
import { BannerImageResponse } from './banner-image.response';

export class BannerSectionResponse {
  @Expose()
  id: string;

  @Expose()
  bannerName: string;

  @Expose()
  bannerType: string;

  @Expose()
  max: number;

  @Expose()
  createdAt: Date;

  @Expose()
  updatedAt: Date;

  deletedAt: Date;

  @Expose()
  bannerImages: Record<string, BannerImageResponse>;

  @Expose()
  bannerDefaultUrl: string;

  constructor(partial: Partial<BannerSectionResponse>) {
    Object.assign(this, partial);
  }
}
