import { Expose } from 'class-transformer';

export class BannerImageResponse {
  @Expose()
  id: string;

  @Expose()
  bannerPosition: string;

  @Expose()
  bannerSectionId: string;

  @Expose()
  directLink: string;

  @Expose()
  heading: string;

  @Expose()
  subHeading: string;

  @Expose()
  position: string;

  constructor(partial: Partial<BannerImageResponse>) {
    Object.assign(this, partial);
  }
}
