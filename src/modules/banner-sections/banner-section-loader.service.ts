import { Injectable, OnModuleInit } from '@nestjs/common';
import { BannerSectionService } from './banner-section.service';
import { BannerSectionType, CommonStatus } from '../../utils/enums';
import { CreateBannerSectionDto } from './dtos/create-banner-section.dto';

@Injectable()
export class BannerSectionLoaderService implements OnModuleInit {
  constructor(private readonly bannerSectionService: BannerSectionService) {}

  async onModuleInit() {
    const bannerSections = Object.values(BannerSectionType);

    for (const type of bannerSections) {
      const bannerSection = await this.bannerSectionService.findOneByType(type);

      if (bannerSection) continue;

      const bannerCreate = new CreateBannerSectionDto({
        bannerName: 'Test',
        max: 3,
        bannerType: type,
        status: CommonStatus.active,
        bannerDefaultUrl: '/images/banners/banner_1.jpg',
      });

      switch (type) {
        case BannerSectionType.sectionBanner: {
          bannerCreate.bannerName = 'Slider trang chủ';
          bannerCreate.max = 3;
          bannerCreate.bannerDefaultUrl = 'files/default/banner-home.webp';
          break;
        }
        case BannerSectionType.sectionUnderBanner: {
          bannerCreate.bannerName = 'Banner dưới slider';
          bannerCreate.max = 4;
          bannerCreate.bannerDefaultUrl =
            'files/default/banner-under-slider.webp';
          break;
        }
        case BannerSectionType.sectionBannerProduct: {
          bannerCreate.bannerName = 'Banner sản phẩm';
          bannerCreate.max = 3;
          bannerCreate.bannerDefaultUrl = 'files/default/banner-product.webp';
          break;
        }
        // case BannerSectionType.sectionInstagram: {
        //   bannerCreate.bannerName = 'Banner Instagram';
        //   bannerCreate.max = 12;
        //   bannerCreate.bannerDefaultUrl = 'files/default/banner-instagram.webp';
        //   break;
        // }
        case BannerSectionType.sectionMiddle: {
          bannerCreate.bannerName = 'Banner nội dung ở giữa';
          bannerCreate.max = 2;
          bannerCreate.bannerDefaultUrl = 'files/default/banner-center.webp';
          break;
        }
        case BannerSectionType.sectionPopup: {
          bannerCreate.bannerName = 'Banner quảng cáo';
          bannerCreate.max = 1;
          bannerCreate.bannerDefaultUrl =
            'files/default/banner-advertisement.webp';
          break;
        }
      }

      await this.bannerSectionService.create(
        new CreateBannerSectionDto(bannerCreate),
      );
    }
  }
}
