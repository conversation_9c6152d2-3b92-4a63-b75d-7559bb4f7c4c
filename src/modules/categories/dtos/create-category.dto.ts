import { ApiProperty } from '@nestjs/swagger';
import {
  IsMongoId,
  IsNotEmpty,
  IsString,
  MaxLength,
  <PERSON><PERSON>ength,
  ValidateIf,
} from 'class-validator';
import {
  CategoryType,
  CommonStatus,
  ValidatorFields,
} from '../../../utils/enums';
import { description } from '../../../utils/descriptions';
import { ImageDto } from '../../shared/dto/image.dto';

export class CreateCategoryDto extends ImageDto {
  @ApiProperty({
    type: 'string',
    required: true,
    description: description.categories.validator.name,
  })
  @IsNotEmpty()
  @IsString()
  @MaxLength(ValidatorFields.strMaxLength)
  @MinLength(ValidatorFields.strMinLength)
  categoryName: string;

  @ApiProperty({
    type: 'string',
    required: true,
    description: description.common.slug,
  })
  @MaxLength(ValidatorFields.strMaxLength)
  @MinLength(ValidatorFields.strMinLength)
  slug: string;

  @ApiProperty({
    type: 'string',
    required: false,
    description: description.categories.validator.parent,
  })
  @ValidateIf((dto) => dto.parentId)
  @IsMongoId()
  parentId?: string;

  @ApiProperty({
    type: 'string',
    required: true,
    description: description.categories.validator.type,
    enum: Object.values(CategoryType),
  })
  @IsString()
  categoryType: string;

  @ApiProperty({
    enum: Object.values(CommonStatus),
    default: CommonStatus.active,
    description: description.common.status,
    required: true,
  })
  @IsString()
  status: string;

  constructor(data: Partial<CreateCategoryDto>) {
    super(data);
    Object.assign(this, data);
  }
}
