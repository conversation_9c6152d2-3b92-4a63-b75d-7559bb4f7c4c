import { ApiProperty } from '@nestjs/swagger';
import { CreateCategoryDto } from './create-category.dto';
import { description } from '../../../utils/descriptions';
import { IsMongoId } from 'class-validator';
export class UpdateCategoryDto extends CreateCategoryDto {
  @ApiProperty({
    type: 'string',
    required: true,
    description: description.categories.validator.id,
  })
  @IsMongoId()
  id: string;
}
