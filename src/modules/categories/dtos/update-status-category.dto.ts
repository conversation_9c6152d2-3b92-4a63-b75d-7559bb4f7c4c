import { ApiProperty } from '@nestjs/swagger';
import { description } from '../../../utils/descriptions';
import { IsMongoId, IsString } from 'class-validator';
import { CommonStatus } from '../../../utils/enums';

export class UpdateStatusCategorylDto {
  @ApiProperty({
    type: 'string',
    required: true,
    description: description.categories.validator.id,
  })
  @IsMongoId()
  id: number;

  @ApiProperty({
    enum: Object.values(CommonStatus),
    default: CommonStatus.active,
    description: description.common.status,
    required: true,
  })
  @IsString()
  status: string;
}
