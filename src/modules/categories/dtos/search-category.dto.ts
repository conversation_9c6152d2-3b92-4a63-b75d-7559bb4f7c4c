import { ApiProperty } from '@nestjs/swagger';
import { description } from '../../../utils/descriptions';
import { IsOptional, IsString } from 'class-validator';
import { CategoryType } from '../../../utils/enums';

export class SearchCategorylDto {
  @ApiProperty({
    type: 'string',
    required: false,
    description: description.categories.validator.name,
  })
  @IsString()
  @IsOptional()
  categoryName: string;

  @ApiProperty({
    type: 'string',
    required: true,
    description: description.categories.validator.type,
    enum: Object.values(CategoryType),
  })
  @IsString()
  @IsOptional()
  categoryType: string;
}
