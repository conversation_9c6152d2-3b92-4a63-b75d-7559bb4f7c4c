import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import mongoose, { HydratedDocument } from 'mongoose';
import { CommonStatus } from '../../../utils/enums';
import { BaseSchema } from '../../shared/base/base.schema';
import { persitImageToDB } from '../../../utils/image';
import { configSchema } from '../../../utils/schema';

export type CategoryDocument = HydratedDocument<Category>;

@Schema(configSchema)
export class Category extends BaseSchema<Category> {
  @Prop({
    type: String,
    required: true,
  })
  categoryName: string;

  @Prop({
    type: String,
    required: true,
  })
  slug: string;

  @Prop({
    type: mongoose.Schema.Types.ObjectId,
  })
  parentId: string;

  @Prop({
    type: String,
    required: true,
  })
  categoryType: string;

  @Prop({
    type: String,
    enum: Object.values(CommonStatus),
    default: CommonStatus.active,
    required: true,
  })
  status: string;

  @Prop({
    type: Object,
    required: true,
    default: {},
    set: (data) => {
      return persitImageToDB(data);
    },
  })
  images: Record<string, string>;
}

export const CategorySchema = SchemaFactory.createForClass(Category);
