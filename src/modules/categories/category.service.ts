import { Model, Types } from 'mongoose';
import { Injectable } from '@nestjs/common';
import { CreateCategoryDto } from './dtos/create-category.dto';
import { UpdateCategoryDto } from './dtos/update-category.dto';
import { Category } from './schemas/category.schema';
import { InjectModel } from '@nestjs/mongoose';
import { SearchCategorylDto } from './dtos/search-category.dto';
import { CommonEvent, FilePage } from '../../utils/enums';
import { CategoryCreatedEvent } from '../events/created/category-created.event';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { MetadataResponse } from '../../utils/response/meta.response';

@Injectable()
export class CategoryService {
  constructor(
    @InjectModel(Category.name) private categoryModel: Model<Category>,
    private eventEmitter: EventEmitter2,
  ) {}

  async findAll(
    dto: SearchCategorylDto,
  ): Promise<MetadataResponse<Category[]>> {
    const categoryEntities = await this.categoryModel
      .find({
        ...(dto.categoryName
          ? {
              categoryName: dto.categoryName,
            }
          : {}),
        ...(dto.categoryType
          ? {
              categoryType: dto.categoryType,
            }
          : {}),
        deletedAt: {
          $eq: null,
        },
      })
      .exec();
    const categoryJson = categoryEntities.map((cat) => ({
      ...cat.toJSON(),
      parentId: cat.parentId ? String(cat.parentId) : null,
    }));
    const queryTotal = await this.categoryModel
      .find({
        deletedAt: {
          $eq: null,
        },
        ...(dto.categoryType
          ? {
              categoryType: dto.categoryType,
            }
          : {}),
      })
      .exec();
    return {
      data: categoryJson,
      metadata: {
        totalRows: queryTotal.length,
      },
    };
  }

  async findOne(id: Types.ObjectId): Promise<Category> {
    const category = await this.categoryModel
      .findOne({
        _id: id,
        deletedAt: null,
      })
      .exec();
    return category.toJSON();
  }

  async create(dto: CreateCategoryDto): Promise<Category> {
    const created = await this.categoryModel.create(dto);

    this.eventEmitter.emit(
      CommonEvent.categoryCreated,
      new CategoryCreatedEvent({
        id: created._id,
        filename: String(dto.images),
        page: FilePage.categories,
      }),
    );
    return created;
  }

  async update(payload: UpdateCategoryDto): Promise<Category> {
    const updated = this.categoryModel.findOneAndUpdate(
      {
        _id: payload.id,
        deletedAt: null,
      },
      {
        $set: payload,
      },
      { new: true }, // return old value before update set false
    );
    return updated;
  }

  async delete(id: Types.ObjectId): Promise<Category> {
    const deleted = this.categoryModel.findOneAndUpdate(
      {
        _id: id,
        deletedAt: null,
      },
      {
        $set: {
          deletedAt: new Date(),
        },
      },
      { new: true }, // return old value before update set false
    );
    return deleted;
  }
}
