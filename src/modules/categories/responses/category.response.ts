import { Expose, Transform, Type } from 'class-transformer';
import { Category } from '../schemas/category.schema';

type ImageRecord = Record<string, string>;
export class CategoryResponse {
  @Expose()
  id: string;

  @Expose()
  categoryName: string;

  @Expose()
  slug: string;

  @Expose()
  parentId: string;

  @Expose()
  categoryType: string;

  @Expose()
  status: string;

  @Expose()
  createdAt: Date;

  @Expose()
  updatedAt: Date;

  deletedAt: Date;

  @Expose()
  @Transform(({ obj }) => obj.images || {})
  images: ImageRecord;

  @Expose()
  @Transform(({ obj }) => obj.imagesFull || {})
  imagesFull: ImageRecord;

  @Expose()
  @Type(() => CategoryResponse)
  childs: CategoryResponse[];

  constructor(partial: Partial<Category>) {
    Object.assign(this, partial);
  }
}
