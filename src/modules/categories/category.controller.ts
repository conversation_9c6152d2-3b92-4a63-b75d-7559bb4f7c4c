import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
  UseInterceptors,
} from '@nestjs/common';
import { CategoryService } from './category.service';
import { CreateCategoryDto } from './dtos/create-category.dto';
import {
  ApiBadRequestResponse,
  ApiForbiddenResponse,
  ApiInternalServerErrorResponse,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiOperation,
  ApiParam,
  ApiTags,
  ApiUnauthorizedResponse,
} from '@nestjs/swagger';
import { RouteName } from '../../utils/enums';
import { getDescription, description } from '../../utils/descriptions';
import { UpdateCategoryDto } from './dtos/update-category.dto';
import { Types } from 'mongoose';
import { ResponseMessage } from '../../decorators/responses/message.decorator';
import { CategoryResponse } from './responses/category.response';
import { SerializeResponse } from '../../decorators/responses/serialize.decorator';
import { SerializerInterceptor } from '../../interceptors/serialize.interceptor';
import { SearchCategorylDto } from './dtos/search-category.dto';
import { RecursionCategoryInterceptor } from '../../decorators/categories/recursion-decorator';
import { MetadataResponse } from '../../utils/response/meta.response';
import { Category } from './schemas/category.schema';

@Controller(`/${RouteName.categories}`)
@ApiTags(RouteName.categories)
@ApiOkResponse({
  description: description.httpStatus.ok,
  isArray: true,
})
@ApiForbiddenResponse({
  description: description.httpStatus.forbidden,
})
@ApiUnauthorizedResponse({ description: description.httpStatus.unAuthorized })
@ApiBadRequestResponse({ description: description.httpStatus.badRequest })
@ApiInternalServerErrorResponse({
  description: description.httpStatus.interalServer,
})
@ApiNotFoundResponse({ description: description.httpStatus.notFound })
export class CategoryController {
  constructor(private readonly categoryService: CategoryService) {}

  @Get()
  @ResponseMessage('Thành công')
  @SerializeResponse(CategoryResponse)
  @ApiOperation({
    summary: getDescription(RouteName.categories, description.controller.gets),
  })
  @UseInterceptors(RecursionCategoryInterceptor, SerializerInterceptor)
  async gets(
    @Query() dto: SearchCategorylDto,
  ): Promise<MetadataResponse<Category[]>> {
    return this.categoryService.findAll(dto);
  }

  @Get('/:id')
  @ApiParam({
    name: 'id',
    type: 'string',
    required: true,
    description: description.categories.validator.id,
  })
  @ApiOperation({
    summary: getDescription(
      RouteName.categories,
      description.controller.getOne,
    ),
  })
  async get(@Param('id') id: Types.ObjectId): Promise<Category> {
    return this.categoryService.findOne(id);
  }

  @Post()
  @ApiOperation({
    summary: getDescription(RouteName.categories, description.controller.post),
  })
  async create(@Body() payload: CreateCategoryDto): Promise<Category> {
    return this.categoryService.create(payload);
  }

  @Put()
  @ApiOperation({
    summary: getDescription(RouteName.categories, description.controller.put),
  })
  async update(@Body() payload: UpdateCategoryDto): Promise<Category> {
    return this.categoryService.update(payload);
  }

  @Delete('/:id')
  @ApiParam({
    name: 'id',
    type: 'string',
    required: true,
    description: description.categories.validator.id,
  })
  @ApiOperation({
    summary: getDescription(
      RouteName.categories,
      description.controller.getOne,
    ),
  })
  async delete(@Param('id') id: Types.ObjectId): Promise<Category> {
    return this.categoryService.delete(id);
  }
}
