import { ProductModule } from './products/product.module';
import { Module } from '@nestjs/common';
import { CategoryModule } from './categories/category.module';
import { HealthModule } from './health/health.module';
import { SettingModule } from './settings/setting.module';
import { OrderModule } from './orders/order.module';
import { AttributeModule } from './attributes/attribute.module';
import { PropertyModule } from './properties/property.module';
import { AttributePropertyModule } from './attribute-properties/attribute-property.module';
import { MongoModule } from '../configs/database/mongo.module';
import { ConfigModule } from '@nestjs/config';
import { EnumModule } from './enums/enum.module';
import { LocationModule } from './locations/location.module';
import { CustomerModule } from './customers/customer.module';
import { ShipFeeModule } from './ship-fee/ship-fee.module';
import { UserModule } from './users/user.module';
import { RoleModule } from './roles/role.module';
import { AuthModule } from './auth/auth.module';
import { FileModule } from './files/file.module';
import { ServeStaticModule } from '@nestjs/serve-static';
import { join } from 'path';
import { EventModule } from './events/event.module';
import { BannerSectionModule } from './banner-sections/banner-section.module';
import { BannerImageModule } from './banners-images/banner-image.module';
import { CloudinaryModule } from './cloudinary/cloudinary.module';
import { BlogModule } from './blogs/blogs.module';
import { ApplicationModule } from './applications/applications.module';
import { NestFactory } from '@nestjs/core';
import { NestExpressApplication } from '@nestjs/platform-express';
import { SocialMediaModule } from './social-media/social-media.module';
import { PermissionModule } from './permissions/permission.module';
import { RolePermissionModule } from './role-permissions/role-permission.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    ServeStaticModule.forRoot({
      rootPath: join(__dirname, '..', '..', `${process.env.UPLOAD_FOLDER}`),
      // Tell NestJS to serve the files under ~/uploads/
      serveRoot: `/${process.env.UPLOAD_FOLDER}/`,
    }),
    MongoModule,
    HealthModule,
    AuthModule,
    CategoryModule,
    ProductModule,
    AttributeModule,
    PropertyModule,
    AttributePropertyModule,
    OrderModule,
    BlogModule,
    SettingModule,
    EnumModule,
    LocationModule,
    CustomerModule,
    LocationModule,
    ShipFeeModule,
    UserModule,
    RoleModule,
    FileModule,
    EventModule,
    BannerSectionModule,
    BannerImageModule,
    CloudinaryModule,
    ApplicationModule,
    SocialMediaModule,
    PermissionModule,
    RolePermissionModule,
  ],
  controllers: [],
  providers: [],
})
export class AppModule {
  async onModuleInit() {
    const app = await NestFactory.create<NestExpressApplication>(AppModule);
    app.setBaseViewsDir(join(__dirname, '..', 'views'));
    app.setViewEngine('hbs');
  }
}
