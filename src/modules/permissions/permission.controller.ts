import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
} from '@nestjs/common';
import {
  ApiBadRequestResponse,
  ApiForbiddenResponse,
  ApiInternalServerErrorResponse,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiOperation,
  ApiParam,
  ApiTags,
  ApiUnauthorizedResponse,
} from '@nestjs/swagger';
import { RouteName } from '../../utils/enums';
import { getDescription, description } from '../../utils/descriptions';
import { PermissionService } from './permission.service';
import { CreatePermissionDto } from './dtos/create-permission.dto';
import { UpdatePermissionDto } from './dtos/update-permission.dto';
import { SearchPermissionDto } from './dtos/search-permission.dto';
import { Permission } from './schemas/permission.schema';
import { Types } from 'mongoose';
import { MetadataResponse } from '../../utils/response/meta.response';

@Controller(`/${RouteName.permissions}`)
@ApiTags(RouteName.permissions)
@ApiOkResponse({
  description: description.httpStatus.ok,
  isArray: true,
})
@ApiForbiddenResponse({
  description: description.httpStatus.forbidden,
})
@ApiUnauthorizedResponse({ description: description.httpStatus.unAuthorized })
@ApiBadRequestResponse({ description: description.httpStatus.badRequest })
@ApiInternalServerErrorResponse({
  description: description.httpStatus.interalServer,
})
@ApiNotFoundResponse({ description: description.httpStatus.notFound })
export class PermissionController {
  constructor(private readonly permissionService: PermissionService) {}

  @Get()
  @ApiOperation({
    summary: getDescription(RouteName.permissions, description.controller.gets),
  })
  async gets(
    @Query() searchDto: SearchPermissionDto,
  ): Promise<MetadataResponse<Permission[]>> {
    return this.permissionService.findAll(searchDto);
  }

  @Post()
  @ApiOperation({
    summary: getDescription(RouteName.permissions, description.controller.post),
  })
  async create(
    @Body() createPermissionDto: CreatePermissionDto,
  ): Promise<Permission> {
    return this.permissionService.create(createPermissionDto);
  }

  @Put()
  @ApiOperation({
    summary: getDescription(RouteName.permissions, description.controller.put),
  })
  async update(
    @Body() updatePermissionDto: UpdatePermissionDto,
  ): Promise<Permission> {
    return this.permissionService.update(updatePermissionDto);
  }

  @Get('/:id')
  @ApiParam({
    name: 'id',
    type: 'string',
    required: true,
    description: description.permissions.validator.id,
  })
  @ApiOperation({
    summary: getDescription(
      RouteName.permissions,
      description.controller.getOne,
    ),
  })
  async get(@Param('id') id: Types.ObjectId): Promise<Permission> {
    return this.permissionService.findOne(id);
  }

  @Delete('/:id')
  @ApiParam({
    name: 'id',
    type: 'string',
    required: true,
    description: description.permissions.validator.id,
  })
  @ApiOperation({
    summary: getDescription(
      RouteName.permissions,
      description.controller.delete,
    ),
  })
  async delete(@Param('id') id: Types.ObjectId): Promise<Permission> {
    return this.permissionService.remove(id);
  }
}
