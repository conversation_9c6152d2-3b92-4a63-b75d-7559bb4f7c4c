import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsString } from 'class-validator';
import { CommonStatus } from '../../../utils/enums';
import { PageOptionsDto } from '../../../utils/pagination/dtos/page-option.dto';
import { description } from '../../../utils/descriptions';

export class SearchPermissionDto extends PageOptionsDto {
  @ApiProperty({
    type: 'string',
    required: false,
    description: description.permissions.validator.permissionName,
  })
  @IsString()
  @IsOptional()
  permissionName?: string;

  @ApiProperty({
    enum: Object.values(CommonStatus),
    required: false,
    description: description.common.status,
  })
  @IsEnum(CommonStatus)
  @IsOptional()
  status?: string;

  constructor(data: Partial<SearchPermissionDto>) {
    super();
    Object.assign(this, data);
  }
}
