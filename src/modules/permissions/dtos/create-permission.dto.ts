import { ApiProperty } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsOptional,
  IsString,
  MaxLength,
  MinLength,
} from 'class-validator';
import { description } from '../../../utils/descriptions';
import { ValidatorFields } from '../../../utils/enums';

export class CreatePermissionDto {
  @ApiProperty({
    type: 'string',
    required: true,
    description: description.permissions.validator.permissionName,
  })
  @IsNotEmpty()
  @IsString()
  @MaxLength(ValidatorFields.strMaxLength)
  @MinLength(ValidatorFields.strMinLength)
  permissionName: string;

  @ApiProperty({
    type: 'string',
    required: false,
    description: description.permissions.validator.description,
  })
  @IsString()
  @IsOptional()
  @MaxLength(ValidatorFields.strMaxLength)
  description?: string;

  constructor(data: Partial<CreatePermissionDto>) {
    Object.assign(this, data);
  }
}
