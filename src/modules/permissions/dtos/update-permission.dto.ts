import { ApiProperty } from '@nestjs/swagger';
import { IsMongoId } from 'class-validator';
import { description } from '../../../utils/descriptions';
import { CreatePermissionDto } from './create-permission.dto';

export class UpdatePermissionDto extends CreatePermissionDto {
  @ApiProperty({
    type: 'string',
    required: true,
    description: description.permissions.validator.id,
  })
  @IsMongoId()
  id: number;

  constructor(data: Partial<UpdatePermissionDto>) {
    super(data);
    Object.assign(this, data);
  }
}
