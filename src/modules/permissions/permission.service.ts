import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { Permission } from './schemas/permission.schema';
import { CreatePermissionDto } from './dtos/create-permission.dto';
import { UpdatePermissionDto } from './dtos/update-permission.dto';
import { SearchPermissionDto } from './dtos/search-permission.dto';
import { MetadataResponse } from '../../utils/response/meta.response';

@Injectable()
export class PermissionService {
  constructor(
    @InjectModel(Permission.name) private permissionModel: Model<Permission>,
  ) {}

  async create(createPermissionDto: CreatePermissionDto): Promise<Permission> {
    const createdPermission = new this.permissionModel(createPermissionDto);
    return createdPermission.save();
  }

  async findAll(
    searchDto: SearchPermissionDto,
  ): Promise<MetadataResponse<Permission[]>> {
    const page = Number(searchDto.page) || 1;
    const limit = Number(searchDto.limit) || 10;

    const filter: any = { deletedAt: null };
    if (searchDto.permissionName) {
      filter.permissionName = {
        $regex: searchDto.permissionName,
        $options: 'i',
      };
    }
    if (searchDto.status) {
      filter.status = searchDto.status;
    }

    const permissions = await this.permissionModel
      .find(filter)
      .limit(limit)
      .skip((page - 1) * limit)
      .sort({ createdAt: -1 })
      .exec();

    const total = await this.permissionModel.countDocuments(filter);

    return {
      data: permissions,
      metadata: {
        totalRows: total,
        page: page,
        limit: limit,
        numberOfPage: Math.ceil(total / limit),
      },
    };
  }

  async findOne(id: Types.ObjectId): Promise<Permission> {
    return this.permissionModel.findOne({ _id: id, deletedAt: null }).exec();
  }

  async update(updatePermissionDto: UpdatePermissionDto): Promise<Permission> {
    return this.permissionModel
      .findByIdAndUpdate(updatePermissionDto.id, updatePermissionDto, {
        new: true,
      })
      .exec();
  }

  async remove(id: Types.ObjectId): Promise<Permission> {
    return this.permissionModel
      .findByIdAndUpdate(id, { deletedAt: new Date() }, { new: true })
      .exec();
  }
}
