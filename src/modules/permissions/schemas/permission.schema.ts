import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument } from 'mongoose';
import { BaseSchema } from '../../shared/base/base.schema';
import { configSchema } from '../../../utils/schema';

export type PermissionDocument = HydratedDocument<Permission>;

@Schema(configSchema)
export class Permission extends BaseSchema<Permission> {
  @Prop({
    type: String,
    required: true,
  })
  permissionName: string;

  @Prop({
    type: String,
    required: false,
  })
  description: string;
}

export const PermissionSchema = SchemaFactory.createForClass(Permission);
