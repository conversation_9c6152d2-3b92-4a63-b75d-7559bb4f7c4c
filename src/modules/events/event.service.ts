import { Injectable, Logger } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { CommonEvent } from '../../utils/enums';
import { SettingCreatedEvent } from './created/setting-created.event';
import { FileService } from '../files/file.service';
import { CreateFileDto } from '../files/dtos/create-file.dto';

@Injectable()
export class EventService {
  private readonly logger = new Logger(FileService.name);
  constructor(private fileService: FileService) {}

  @OnEvent(CommonEvent.settingCreated)
  handleSettingCreatedEvent(payload: SettingCreatedEvent) {
    if (
      payload.filename ||
      (Array.isArray(payload.filename) && payload.filename.length)
    ) {
      this.logger.log(`Event sent: page ${payload.page} id ${payload.id}`);
      this.fileService.bulkCreate(
        new CreateFileDto({
          filename: String(payload.filename),
          referenceId: payload.id,
          page: payload.page,
        }),
      );
    }
  }
}
