import { Injectable } from '@nestjs/common';
import { Model, Types } from 'mongoose';
import { InjectModel } from '@nestjs/mongoose';
import { Enums } from './schemas/enum.schema';
import { SearchEnumDto } from './dtos/search-enum.dto';
import { UpdateEnumDto } from './dtos/update-enum.dto';
import { MetadataResponse } from '../../utils/response/meta.response';

@Injectable()
export class EnumService {
  constructor(@InjectModel(Enums.name) private enumModel: Model<Enums>) {}

  async getEnumValues(): Promise<string[]> {
    const enumDocuments = await this.enumModel.find().exec();
    return enumDocuments.map((doc) => doc.value);
  }

  async getEnum(name: string[]): Promise<Record<string, Enums>> {
    const query = await this.enumModel
      .find({
        name: {
          $in: name,
        },
      })
      .exec();
    return query.reduce((result, current) => {
      const convertedType = current.json.map((child) => ({
        name: child.name,
        key: child.key,
        value: current.type === 'number' ? Number(child.value) : child.value,
      }));
      result[current.name] = {
        ...current.toJSON(),
        json: convertedType,
      };
      return result;
    }, {});
  }

  async findEnums(dto: SearchEnumDto): Promise<MetadataResponse<Enums[]>> {
    const page = Number(dto.page) || 1;
    const limit = Number(dto.limit) || 10;
    const query = await this.enumModel
      .find()
      .limit(limit)
      .skip((page - 1) * limit)
      .exec();
    const queryTotal = await this.enumModel.find({}).exec();
    return {
      data: query,
      metadata: {
        totalRows: queryTotal.length,
        page: page,
        limit: limit,
      },
    };
  }

  async findOne(id: Types.ObjectId): Promise<Enums> {
    return this.enumModel.findById(id).exec();
  }

  async update(payload: UpdateEnumDto): Promise<Enums> {
    const updated = this.enumModel.findOneAndUpdate(
      {
        _id: payload.id,
      },
      {
        $set: {
          json: payload.json,
        },
      },
      { new: true }, // return old value before update set false
    );
    return updated;
  }

  async bulkWrite(payload: Enums[]): Promise<any> {
    //Usage of bulkWrite() method
    this.enumModel.bulkWrite(
      payload.map((item) => ({
        updateOne: {
          filter: { name: item.name },
          update: {
            $setOnInsert: item,
          },
          upsert: true,
        },
      })),
    );
  }
}
