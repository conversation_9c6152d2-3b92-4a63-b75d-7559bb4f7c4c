import { Module } from '@nestjs/common';
import { EnumService } from './enum.service';
import { MongooseModule } from '@nestjs/mongoose';
import { Enums, EnumSchema } from './schemas/enum.schema';
import { EnumLoaderService } from './enum-loader.service';
import { EnumController } from './enum.controller';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Enums.name, schema: EnumSchema, collection: 'enums' },
    ]),
  ],
  controllers: [EnumController],

  providers: [EnumService, EnumLoaderService],
})
export class EnumModule {}
