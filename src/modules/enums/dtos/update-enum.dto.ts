import { IsMongoId, ValidateNested } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { description } from '../../../utils/descriptions';
import { UpdateEnumChildDto } from './update-enum-child.dto';
import { Type } from 'class-transformer';

export class UpdateEnumDto {
  @ApiProperty({
    type: 'string',
    required: true,
    description: description.enums.validator.id,
  })
  @IsMongoId()
  id: number;

  @ApiProperty({ type: UpdateEnumChildDto, isArray: true })
  @ValidateNested({ each: true })
  @Type(() => UpdateEnumChildDto)
  json: UpdateEnumChildDto[];
}
