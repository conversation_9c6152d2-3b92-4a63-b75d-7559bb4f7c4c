import { IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { description } from '../../../utils/descriptions';
import { Optional } from '@nestjs/common';

export class UpdateEnumChildDto {
  @ApiProperty({
    type: 'string',
    required: true,
    description: description.enums.validator.key,
  })
  @IsString()
  key: string;

  @ApiProperty({
    type: 'string',
    required: true,
    description: description.enums.validator.key,
  })
  @IsString()
  value: string;

  @ApiProperty({
    type: 'string',
    required: false,
    description: description.enums.validator.name,
  })
  @IsString()
  @Optional()
  name: string;
}
