import { Body, Controller, Get, Param, Put, Query } from '@nestjs/common';
import { EnumService } from './enum.service';
import {
  ApiBadRequestResponse,
  ApiForbiddenResponse,
  ApiInternalServerErrorResponse,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiOperation,
  ApiParam,
  ApiTags,
  ApiUnauthorizedResponse,
} from '@nestjs/swagger';
import { RouteName } from '../../utils/enums';
import { getDescription, description } from '../../utils/descriptions';
import { ResponseMessage } from '../../decorators/responses/message.decorator';
import { GetEnum } from './dtos/get-enum.dto';
import { SearchEnumDto } from './dtos/search-enum.dto';
import { Types } from 'mongoose';
import { UpdateEnumDto } from './dtos/update-enum.dto';
import { Enums } from './schemas/enum.schema';
import { MetadataResponse } from '../../utils/response/meta.response';

@Controller(`/${RouteName.enums}`)
@ApiTags(RouteName.enums)
@ApiOkResponse({
  description: description.httpStatus.ok,
  isArray: true,
})
@ApiForbiddenResponse({
  description: description.httpStatus.forbidden,
})
@ApiUnauthorizedResponse({ description: description.httpStatus.unAuthorized })
@ApiBadRequestResponse({ description: description.httpStatus.badRequest })
@ApiInternalServerErrorResponse({
  description: description.httpStatus.interalServer,
})
@ApiNotFoundResponse({ description: description.httpStatus.notFound })
export class EnumController {
  constructor(private readonly enumService: EnumService) {}

  @Get()
  @ResponseMessage('Thành công')
  @ApiOperation({
    summary: getDescription(RouteName.enums, description.controller.gets),
  })
  async gets(@Query() query: GetEnum): Promise<Record<string, Enums>> {
    return this.enumService.getEnum(query.fields);
  }

  @Get('/find')
  @ResponseMessage('Thành công')
  @ApiOperation({
    summary: getDescription(RouteName.enums, description.controller.gets),
  })
  async find(@Query() dto: SearchEnumDto): Promise<MetadataResponse<Enums[]>> {
    return this.enumService.findEnums(dto);
  }

  @Get('/:id')
  @ApiParam({
    name: 'id',
    type: 'string',
    required: true,
    description: description.enums.validator.id,
  })
  @ResponseMessage('Thành công')
  @ApiOperation({
    summary: getDescription(RouteName.enums, description.controller.getOne),
  })
  async get(@Param('id') id: Types.ObjectId): Promise<Enums> {
    return this.enumService.findOne(id);
  }

  @Put()
  @ApiOperation({
    summary: getDescription(RouteName.categories, description.controller.put),
  })
  async update(@Body() payload: UpdateEnumDto): Promise<Enums> {
    return this.enumService.update(payload);
  }
}
