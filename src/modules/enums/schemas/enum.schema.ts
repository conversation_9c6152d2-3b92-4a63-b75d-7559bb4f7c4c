import { HydratedDocument } from 'mongoose';
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';

export type EnnumDocument = HydratedDocument<Enums>;

@Schema({
  versionKey: false,
  virtuals: true,
  toJSON: {
    getters: true,
    transform: function (_, ret) {
      delete ret._id;
    },
  },
})
export class Enums {
  @Prop({
    type: String,
    required: true,
    unique: true,
    index: true,
  })
  name: string;

  @Prop({
    type: String,
    required: true,
  })
  value: string;

  @Prop({
    type: String,
    required: true,
  })
  type: string;

  @Prop({
    type: Array,
    default: [],
  })
  json: { [key: string]: any }[];
}

export const EnumSchema = SchemaFactory.createForClass(Enums);
