import { Injectable, OnModuleInit } from '@nestjs/common';
import { EnumService } from './enum.service';
import enumApplication from '../../utils/enums';
import { isNumber } from 'class-validator';

@Injectable()
export class EnumLoaderService implements OnModuleInit {
  constructor(private readonly enumService: EnumService) {}

  async onModuleInit() {
    const enums = Object.keys(enumApplication).map((keyMain) => {
      const type = isNumber(Object.values(enumApplication[keyMain])[0])
        ? 'number'
        : 'string';
      return {
        name: keyMain,
        value: keyMain,
        type: type,
        json: Object.keys(enumApplication[keyMain]).map((key) => {
          return {
            key: key,
            value: enumApplication[keyMain][key],
            name: '',
          };
        }),
      };
    });
    this.enumService.bulkWrite(enums);
  }
}
