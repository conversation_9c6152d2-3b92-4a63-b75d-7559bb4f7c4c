// cloudinary.service.ts

import { Injectable } from '@nestjs/common';
import { v2 as cloudinary } from 'cloudinary';
import * as streamifier from 'streamifier';
import { CloudinaryResponse } from './response/cloudinary-response';

@Injectable()
export class CloudinaryService {
  async uploadFileCloud(
    folder: string,
    files: Array<Express.Multer.File>,
  ): Promise<CloudinaryResponse[]> {
    const filesUploaded = [];
    for (const element of files) {
      const result = await new Promise<CloudinaryResponse>(
        (resolve, reject) => {
          const uploadStream = cloudinary.uploader.upload_stream(
            { folder: folder },
            (error, result) => {
              if (error) return reject(error);
              resolve(result);
            },
          );

          streamifier.createReadStream(element.buffer).pipe(uploadStream);
        },
      );
      filesUploaded.push(result);
    }
    return filesUploaded;
  }
}
