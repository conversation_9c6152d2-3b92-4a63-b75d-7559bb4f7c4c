import { Body, Controller, Get, Put } from '@nestjs/common';
import { SettingService } from './setting.service';
import {
  ApiBadRequestResponse,
  ApiForbiddenResponse,
  ApiInternalServerErrorResponse,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiOperation,
  ApiTags,
  ApiUnauthorizedResponse,
} from '@nestjs/swagger';
import { RouteName } from '../../utils/enums';
import { getDescription, description } from '../../utils/descriptions';
import { Setting } from './schemas/setting.schema';
import { UpdateSettingDto } from './dtos/update-setting.dto';
import { SettingResponse } from './reponse/setting-response.dto';
import { UpdateSettingPartialDto } from './dtos/update-setting-partial.dto';

@Controller(`/${RouteName.settings}`)
@ApiTags(RouteName.settings)
@ApiOkResponse({
  description: description.httpStatus.ok,
  isArray: true,
})
@ApiForbiddenResponse({
  description: description.httpStatus.forbidden,
})
@ApiUnauthorizedResponse({ description: description.httpStatus.unAuthorized })
@ApiBadRequestResponse({ description: description.httpStatus.badRequest })
@ApiInternalServerErrorResponse({
  description: description.httpStatus.interalServer,
})
@ApiNotFoundResponse({ description: description.httpStatus.notFound })
export class SettingController {
  constructor(private readonly settingService: SettingService) {}

  @Get()
  @ApiOperation({
    summary: getDescription(RouteName.settings, description.controller.gets),
  })
  async get(): Promise<SettingResponse> {
    return this.settingService.findOne();
  }

  @Put()
  @ApiOperation({
    summary: getDescription(RouteName.settings, description.controller.put),
  })
  async update(@Body() payload: UpdateSettingDto): Promise<Setting> {
    return this.settingService.update(payload);
  }

  @Put('partial-image')
  @ApiOperation({
    summary: getDescription(RouteName.settings, description.controller.put),
  })
  async updatePartialImage(
    @Body() payload: UpdateSettingPartialDto,
  ): Promise<Setting> {
    return this.settingService.updatePartialImage(payload);
  }
}
