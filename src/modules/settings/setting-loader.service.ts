import { Injectable, OnModuleInit } from '@nestjs/common';
import { SettingService } from './setting.service';
import { CreateSettingDto } from './dtos/create-setting.dto';

@Injectable()
export class SettingLoaderService implements OnModuleInit {
  constructor(private readonly settingService: SettingService) {}

  async onModuleInit() {
    const setting = await this.settingService.initFindOne();
    if (!setting) {
      this.settingService.intialCreate(
        new CreateSettingDto({
          companyName: 'Wstore',
          companyEmail: '<EMAIL>',
          companyAddress: '123 <PERSON><PERSON><PERSON><PERSON>, Quận 1 , T<PERSON>',
          companyPhone: '0904312341',
          companyTaxNo: '1231456789',
          companyTaxNoDate: null,
          companyTaxLink: '',
          companyWebsite: 'https://wstore.com',
          images: {},
          facebook: 'https://www.facebook.com/',
          instagram: 'https://www.instagram.com/',
          youtube: 'https://www.youtube.com/',
          tiktok: 'https://tiktok.com/',
        }),
      );
    }
  }
}
