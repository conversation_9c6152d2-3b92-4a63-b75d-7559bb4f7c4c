export class SettingResponse {
  companyName: string;

  companyAddress: string;

  companyEmail: string;

  companyPhone: string;

  companyWebsite: string;

  companyTaxNo: string;

  companyTaxNoDate: Date;

  companyTaxLink: string;

  images: string[];

  imagesFull: string[];

  facebook: string;

  instagram: string;

  youtube: string;

  tiktok: string;

  metaTitle: string;

  metaDescription: string;

  metaKeyword: string;

  verifiedImageLink: string;

  constructor(data: Partial<SettingResponse>) {
    Object.assign(this, data);
  }
}
