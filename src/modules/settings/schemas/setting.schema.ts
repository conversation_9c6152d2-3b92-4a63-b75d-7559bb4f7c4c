import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument } from 'mongoose';
import { configSchema } from '../../../utils/schema';
import moment from 'moment';
import { BaseSchema } from '../../shared/base/base.schema';

export type SettingDocument = HydratedDocument<Setting>;

@Schema(configSchema)
export class Setting extends BaseSchema<Setting> {
  @Prop({
    type: String,
    required: true,
  })
  companyName: string;

  @Prop({
    type: String,
    required: true,
  })
  companyAddress: string;

  @Prop({
    type: String,
    required: true,
  })
  companyEmail: string;

  @Prop({
    type: String,
    required: true,
  })
  companyPhone: string;

  @Prop({
    type: String,
    required: true,
  })
  companyWebsite: string;

  @Prop({
    type: String,
    required: true,
  })
  companyTaxNo: string;

  @Prop({
    type: Date,
    get: (data: Date) => {
      return data && moment(data).format('YYYY-MM-DD');
    },
  })
  companyTaxNoDate: Date;

  @Prop({
    type: String,
  })
  companyTaxLink: string;

  @Prop({
    type: String,
    required: false,
  })
  facebook: string;

  @Prop({
    type: String,
    required: false,
  })
  instagram: string;

  @Prop({
    type: String,
    required: false,
  })
  youtube: string;

  @Prop({
    type: String,
    required: false,
  })
  tiktok: string;

  @Prop({
    type: Object,
    required: true,
    default: {},
  })
  images: Record<string, string>;

  @Prop({
    type: String,
    required: false,
  })
  metaTitle: string;

  @Prop({
    type: String,
    required: false,
  })
  metaDescription: string;

  @Prop({
    type: String,
    required: false,
  })
  metaKeyword: string;

  @Prop({
    type: String,
    required: false,
  })
  verifiedImageLink: string;
}

export const SettingSchema = SchemaFactory.createForClass(Setting);
