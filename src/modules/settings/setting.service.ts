import { SettingCreatedEvent } from './../events/created/setting-created.event';
import { Model } from 'mongoose';
import { Injectable, NotFoundException } from '@nestjs/common';
import { CreateSettingDto } from './dtos/create-setting.dto';
import { Setting } from './schemas/setting.schema';
import { InjectModel } from '@nestjs/mongoose';
import { UpdateSettingDto } from './dtos/update-setting.dto';
import { SettingResponse } from './reponse/setting-response.dto';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { CommonEvent, FilePage, SettingImage } from '../../utils/enums';
import { UpdateSettingPartialDto } from './dtos/update-setting-partial.dto';

@Injectable()
export class SettingService {
  constructor(
    @InjectModel(Setting.name) private settingModel: Model<Setting>,
    private eventEmitter: EventEmitter2,
  ) {}

  async findOne(): Promise<SettingResponse> {
    const entity = await this.settingModel.findOne();
    if (!entity) {
      throw new NotFoundException();
    }
    return new SettingResponse(entity.toJSON());
  }

  async initFindOne(): Promise<Setting> {
    const entity = await this.settingModel.findOne();
    return entity || null;
  }

  async intialCreate(dto: CreateSettingDto): Promise<Setting> {
    const created = await this.settingModel.create(dto);

    this.eventEmitter.emit(
      CommonEvent.settingCreated,
      new SettingCreatedEvent({
        id: created._id,
        filename: String(dto.images),
        page: FilePage.settings,
      }),
    );

    return created;
  }

  async update(dto: UpdateSettingDto): Promise<Setting> {
    console.log('dto', dto);
    const updated = await this.settingModel.findOneAndUpdate(
      {
        _id: dto.id,
        deletedAt: null,
      },
      {
        $set: dto,
      },
      { new: true }, // return old value before update set false
    );

    return updated;
  }

  async updatePartialImage(dto: UpdateSettingPartialDto): Promise<Setting> {
    if (!dto.images.length) {
      return null;
    }

    const setting: any = await this.settingModel.findOne({}, { images: 1 });
    const images = setting.images || {};

    if (setting) {
      const newImages = {};
      for (const image of dto.images) {
        [SettingImage.icon, SettingImage.verified].map((type) => {
          if (
            !images ||
            !images[type] ||
            (image.type === type && images[type].image !== image.image)
          ) {
            newImages[type] = {
              type: type,
              image: image.image,
            };
          }
        });
      }

      if (Object.values(newImages).length) {
        const updated = await this.settingModel.findOneAndUpdate(
          {
            _id: dto.id,
            deletedAt: null,
          },
          {
            $set: {
              images: Object.values(images).length
                ? Object.assign(images, {
                    ...newImages,
                  })
                : newImages,
            },
          },
          { new: true },
        );

        if (String(dto.images)) {
          this.eventEmitter.emit(
            CommonEvent.settingCreated,
            new SettingCreatedEvent({
              id: updated._id,
              filename: String(dto.images),
              page: FilePage.settings,
            }),
          );
        }

        return updated;
      }
    }
    return null;
  }
}
