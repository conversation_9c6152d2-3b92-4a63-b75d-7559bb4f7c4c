import { <PERSON>du<PERSON> } from '@nestjs/common';
import { SettingController } from './setting.controller';
import { SettingService } from './setting.service';
import { MongooseModule } from '@nestjs/mongoose';
import { Setting, SettingSchema } from './schemas/setting.schema';
import { SettingLoaderService } from './setting-loader.service';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Setting.name, schema: SettingSchema, collection: 'settings' },
    ]),
  ],
  controllers: [SettingController],
  providers: [SettingService, SettingLoaderService],
})
export class SettingModule {}
