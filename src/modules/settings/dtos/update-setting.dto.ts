import { Types } from 'mongoose';
import { CreateSettingDto } from './create-setting.dto';
import { IsMongoId } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { description } from '../../../utils/descriptions';
import { Exclude } from 'class-transformer';

export class UpdateSettingDto extends CreateSettingDto {
  @ApiProperty({
    type: 'string',
    required: false,
    description: description.setting.validator.id,
  })
  @IsMongoId()
  id: Types.ObjectId;

  @Exclude()
  images: string | string[] | Record<string, string>;
}
