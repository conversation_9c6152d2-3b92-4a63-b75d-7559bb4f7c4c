import { ApiProperty } from '@nestjs/swagger';
import {
  IsEmail,
  IsNotEmpty,
  IsOptional,
  IsString,
  Length,
  ValidateIf,
} from 'class-validator';
import { ValidatorFields } from '../../../utils/enums';
import { description } from '../../../utils/descriptions';
import { IsPhoneNumber } from '../../../decorators/validators/is-phone-number';
import { ImageDto } from '../../shared/dto/image.dto';

export class CreateSettingDto extends ImageDto {
  @ApiProperty({
    type: 'string',
    required: true,
    description: description.setting.validator.companyName,
  })
  @IsNotEmpty()
  @IsString()
  @Length(ValidatorFields.strMinLength, ValidatorFields.strMaxLength)
  companyName: string;

  @ApiProperty({
    type: 'string',
    required: true,
    description: description.setting.validator.companyAddress,
  })
  @IsNotEmpty()
  @IsString()
  @Length(ValidatorFields.strMinLength, ValidatorFields.strMaxLength)
  companyAddress: string;

  @ApiProperty({
    type: 'string',
    required: true,
    description: description.setting.validator.companyEmail,
  })
  @IsNotEmpty()
  @IsString()
  @IsEmail()
  @Length(ValidatorFields.strMinLength, ValidatorFields.strMaxLength)
  companyEmail: string;

  @ApiProperty({
    type: 'string',
    required: true,
    description: description.setting.validator.companyWebsite,
  })
  @IsNotEmpty()
  @IsString()
  @Length(ValidatorFields.strMinLength, ValidatorFields.strMaxLength)
  @ValidateIf((field) => field.companyTaxLink)
  companyWebsite: string;

  @ApiProperty({
    type: 'string',
    required: true,
    description: description.setting.validator.companyPhone,
  })
  @IsNotEmpty()
  @IsString()
  @IsPhoneNumber()
  companyPhone: string;

  @ApiProperty({
    type: 'string',
    required: true,
    description: description.setting.validator.companyTaxNo,
  })
  @IsNotEmpty()
  @IsString()
  @Length(ValidatorFields.strMinLength, ValidatorFields.strMaxLength)
  companyTaxNo: string;

  @ApiProperty({
    type: 'string',
    required: true,
    description: description.setting.validator.companyTaxNoDate,
  })
  @IsNotEmpty()
  @IsString()
  companyTaxNoDate: string;

  @ApiProperty({
    type: 'string',
    required: true,
    description: description.setting.validator.companyTaxLink,
  })
  @IsString()
  @ValidateIf((field) => field.companyTaxLink)
  @Length(ValidatorFields.strMinLength, ValidatorFields.strMaxLength)
  companyTaxLink: string;

  @ApiProperty({
    type: 'string',
    required: true,
    description: description.setting.validator.facebook,
  })
  @IsString()
  @ValidateIf((field) => field.facebook)
  @Length(ValidatorFields.strMinLength, ValidatorFields.strMaxLength)
  facebook: string;

  @ApiProperty({
    type: 'string',
    required: true,
    description: description.setting.validator.instagram,
  })
  @IsString()
  @ValidateIf((field) => field.instagram)
  @Length(ValidatorFields.strMinLength, ValidatorFields.strMaxLength)
  instagram: string;

  @ApiProperty({
    type: 'string',
    required: true,
    description: description.setting.validator.youtube,
  })
  @IsString()
  @ValidateIf((field) => field.youtube)
  @Length(ValidatorFields.strMinLength, ValidatorFields.strMaxLength)
  youtube: string;

  @ApiProperty({
    type: 'string',
    required: true,
    description: description.setting.validator.tiktok,
  })
  @IsString()
  @ValidateIf((field) => field.tiktok)
  @Length(ValidatorFields.strMinLength, ValidatorFields.strMaxLength)
  tiktok: string;

  @ApiProperty({
    type: 'string',
    required: false,
    description: description.common.metaTitle,
  })
  @IsOptional()
  @IsString()
  metaTitle: string;

  @ApiProperty({
    type: 'string',
    required: false,
    description: description.common.metaDescription,
  })
  @IsOptional()
  @IsString()
  metaDescription: string;

  @ApiProperty({
    type: 'string',
    required: false,
    description: description.common.metaKeyword,
  })
  @IsOptional()
  @IsString()
  metaKeyword: string;

  @ApiProperty({
    type: 'string',
    required: false,
    description: description.setting.validator.verifiedImageLink,
  })
  @IsOptional()
  @IsString()
  verifiedImageLink: string;

  constructor(data: Partial<CreateSettingDto>) {
    super(data);
    Object.assign(this, data);
  }
}
