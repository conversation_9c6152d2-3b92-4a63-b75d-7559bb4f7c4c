import { Types } from 'mongoose';
import { IsMongoId, ValidateNested } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { description } from '../../../utils/descriptions';
import { ImageSettingDto } from './image-setting.dtos';
import { Type } from 'class-transformer';

export class UpdateSettingPartialDto {
  @ApiProperty({
    type: 'string',
    required: false,
    description: description.setting.validator.id,
  })
  @IsMongoId()
  id: Types.ObjectId;

  @ApiProperty({ type: ImageSettingDto, isArray: true })
  @ValidateNested({ each: true })
  @Type(() => ImageSettingDto)
  images: ImageSettingDto[];
}
