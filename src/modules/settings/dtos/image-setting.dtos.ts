import { IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { description } from '../../../utils/descriptions';

export class ImageSettingDto {
  @ApiProperty({
    type: 'string',
    required: false,
    description: description.setting.validator.image,
  })
  @IsString()
  image: string;

  @ApiProperty({
    type: 'string',
    required: false,
    description: description.setting.validator.image,
  })
  @IsString()
  type: string;
}
