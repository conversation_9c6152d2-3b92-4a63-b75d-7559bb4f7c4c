import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, Validate } from 'class-validator';
import { IsStringOrStringArray } from '../../../decorators/validators/is-string-array';

export class ImageDto {
  @ApiProperty({
    additionalProperties: {
      oneOf: [
        { type: 'string' },
        {
          type: 'array',
          items: {
            type: 'string',
          },
        },
      ],
    },
    required: true,
  })
  @Validate(IsStringOrStringArray)
  @IsOptional()
  images: string | string[] | Record<string, string>;

  constructor(data: Partial<ImageDto>) {
    Object.assign(this, data);
  }
}
