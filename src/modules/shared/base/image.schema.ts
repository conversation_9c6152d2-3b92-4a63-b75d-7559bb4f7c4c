import { Prop } from '@nestjs/mongoose';
import { BaseSchema } from './base.schema';
import { persitImageToDB } from '../../../utils/image';

export class ImageSchema<T> extends BaseSchema<T> {
  @Prop({
    type: Object,
    required: true,
    default: {},
    get: (data) => {
      return data ? Object.values(data).map((value) => value) : null;
    },
    set: (data) => {
      return persitImageToDB(data);
    },
  })
  images: Record<string, string>;
}
