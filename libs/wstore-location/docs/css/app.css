body {
    padding-top: 56px;
}

td:hover {
    background: #fafafa;
}

/* Github Button */
.github {
    padding: 8px 12px;
    border-radius: 50px;
    color: #fff;
    display: inline-flex;
    font-weight: 600;
    align-items: center;
    justify-content: center;
}

.github svg {
    width: 24px;
    flex-shrink: 0;
    margin-right: 6px;
}

/* Tooltip */
.tooltip .tooltip-text {
    visibility: hidden;
    text-align: center;
    padding: 2px 6px;
    position: absolute;
    z-index: 100;
}

.tooltip:hover .tooltip-text {
    visibility: visible;
}

/* Code Formatting */
pre {
    font-family: ibm-plex-mono, Consolas, Monaco, 'Lucida Console', 'Liberation Mono', 'DejaVu Sans Mono', 'Bitstream Vera Sans Mono', 'Courier New';
    overflow: hidden;
    background: rgb(28, 29, 33);
    color: rgb(192, 197, 206);
    padding: 1rem;
    border-radius: 6px;
    font-size: 13px
}

/* Github Corner */
.github-corner:hover .octo-arm {
    animation: octocat-wave 560ms ease-in-out;
}

@keyframes octocat-wave {

    0%,
    100% {
        transform: rotate(0);
    }

    20%,
    60% {
        transform: rotate(-25deg);
    }

    40%,
    80% {
        transform: rotate(10deg);
    }
}

@media (max-width: 500px) {
    .github-corner:hover .octo-arm {
        animation: none;
    }

    .github-corner .octo-arm {
        animation: octocat-wave 560ms ease-in-out;
    }
}

/* Hidden Element */
textarea {
    position: absolute;
    left: -100%;
}

.emoji, #modal-code {
    font-family: "Twemoji Country Flags", ibm-plex-mono, Consolas, Monaco, 'Lucida Console', 'Liberation Mono', 'DejaVu Sans Mono', 'Bitstream Vera Sans Mono', 'Courier New';
}
