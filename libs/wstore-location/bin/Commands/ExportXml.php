<?php

namespace bin\Commands;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Symfony\Component\Filesystem\Filesystem;
use Spatie\ArrayToXml\ArrayToXml;

class ExportXml extends Command
{
    protected static $defaultName = 'export:xml';
    protected static $defaultDescription = 'Export data to XML format';

    private const FILES = [
        'regions' => ['from' => '/json/regions.json', 'to' => '/xml/regions.xml', 'singular' => 'region'],
        'subregions' => ['from' => '/json/subregions.json', 'to' => '/xml/subregions.xml', 'singular' => 'subregion'],
        'countries' => ['from' => '/json/countries.json', 'to' => '/xml/countries.xml', 'singular' => 'country'],
        'states' => ['from' => '/json/states.json', 'to' => '/xml/states.xml', 'singular' => 'state'],
        'cities' => ['from' => '/json/cities.json', 'to' => '/xml/cities.xml', 'singular' => 'city'],
    ];

    private Filesystem $filesystem;

    public function __construct()
    {
        parent::__construct(self::$defaultName);
        $this->filesystem = new Filesystem();
    }

    protected function configure(): void
    {
        $this->setHelp('This command exports the database to XML format');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        $rootDir = dirname(PATH_BASE);

        $io->title('Exporting XML data to ' . $rootDir);

        try {
            foreach (self::FILES as $root => $config) {
                $io->section("Processing: $root");

                $jsonData = $this->filesystem->exists($rootDir . $config['from'])
                    ? file_get_contents($rootDir . $config['from'])
                    : throw new \RuntimeException("JSON file not found: {$config['from']}");

                $data = json_decode($jsonData, true)
                    ?: throw new \RuntimeException("Invalid JSON in {$config['from']}");

                $xml = ArrayToXml::convert(
                    [$config['singular'] => $data],
                    $root,
                    false,
                    'UTF-8',
                    '1.0',
                    ['formatOutput' => true]
                );

                $this->filesystem->dumpFile($rootDir . $config['to'], $xml);
                $io->success("Exported to {$config['to']}");
            }

            return Command::SUCCESS;
        } catch (\Exception $e) {
            $io->error("Export failed: {$e->getMessage()}");
            return Command::FAILURE;
        }
    }
}
