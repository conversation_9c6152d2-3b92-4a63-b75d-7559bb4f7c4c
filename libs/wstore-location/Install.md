✅ Giải pháp 1: Fork lại repo, xóa file không cần, dùng fork làm submodule
🔧 Các bước:
Fork repo này vào tài khoản GitHub của bạn
👉 https://github.com/dr5hn/countries-states-cities-database → Click Fork

Trên bản fork của bạn (ví dụ yourname/countries-states-cities-database), vào tab Code → chọn main → xóa các file nặng như:

_.sql, _.csv

_.xml, _.xlsx

\*.md không cần

Các thư mục bạn không dùng (như sql/, csv/, xml/, scripts/…)

→ Bạn có thể xóa online hoặc clone repo fork về và xóa local rồi push.

Sau khi clean xong, repo fork của bạn chỉ còn ~3 JSON file:

pgsql
Sao chép
Chỉnh sửa
countries.json
states.json
cities.json
Trong dự án NestJS, thêm submodule trỏ vào repo fork của bạn:

bash
Sao chép
Chỉnh sửa
git submodule add https://github.com/yourname/countries-states-cities-database.git src/geo-data
Khi cần update dữ liệu:

bash
Sao chép
Chỉnh sửa
cd src/geo-data
git pull origin main
✅ Lúc này nhẹ, sạch, dễ kiểm soát, vẫn có thể update như thường từ repo của bạn.

Nếu muốn sync fork với repo gốc sau này:
bash
Sao chép
Chỉnh sửa
git remote add upstream https://github.com/dr5hn/countries-states-cities-database.git
git fetch upstream
git merge upstream/main

Cách làm để sync mà không đưa lại file đã xoá
Cách 1: Dùng .gitattributes để đánh dấu file bị xoá là merge=ours
Trong repo fork của bạn, tạo file .gitattributes (hoặc chỉnh sửa nếu đã có):

markdown
Sao chép
Chỉnh sửa
json/_.csv merge=ours
sql/_ merge=ours
\*.md merge=ours
Đăng ký chiến thuật merge ours cho những file đó:

bash
Sao chép
Chỉnh sửa
git config merge.ours.driver true
Cách 2: Dùng git update-index --assume-unchanged
Đánh dấu file là unchanged:

bash
Sao chép
Chỉnh sửa
git update-index --assume-unchanged <file>
Để bỏ đánh dấu:

bash
Sao chép
Chỉnh sửa
git update-index --no-assume-unchanged <file>
Để liệt kê danh sách file đã đánh dấu:

bash
Sao chép
Chỉnh sửa
git ls-files -v | grep ^h

repo của mình
git remote set-<NAME_EMAIL>:chitrung252/wstore-location.git
**************:chitrung252/wstore-location.git

Đã cài upsteam rồi
sau này chỉ cần
git fetch upstream
git merge upstream/main
