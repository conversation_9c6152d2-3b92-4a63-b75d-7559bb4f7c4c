/* eslint-disable @typescript-eslint/no-var-requires */
require('dotenv').config();
const path = require('path');
const fs = require('fs');
const { MongoClient } = require('mongodb');

(async () => {
  const uri = process.env.DB_URL;
  const client = new MongoClient(uri);

  try {
    await client.connect();
    const database = client.db('wstore');

    const enumValues = await database.collection('enums').find().toArray();
    console.log('enumValues', enumValues);
    const enumContent = enumValues.map(
      (item) =>
        `export enum ${item.name} {
  ${item.json
    .map(
      (val) =>
        `${val.key} = ${
          item.type === 'number' ? Number(val.value) : `'${val.value}'`
        },`,
    )
    .join(`\n  `)}`,
    );

    enumContent.push('');
    const filePath = path.join(__dirname, '../src/modules/enums/enum-db.ts'); // Adjust the path as necessary

    fs.writeFileSync(filePath, enumContent.join('\n}\n'), { encoding: 'utf8' });
    console.log(`Enum file generated at ${filePath}`);
  } catch (e) {
    console.error(e);
  } finally {
    await client.close();
  }
})();
