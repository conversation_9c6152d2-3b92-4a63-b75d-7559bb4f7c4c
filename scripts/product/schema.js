/* eslint-disable @typescript-eslint/no-var-requires */
const mongoose = require('mongoose');

const baseSchema = {
  createdAt: {
    required: true,
    default: new Date(),
    type: Date,
  },
  updatedAt: {
    required: true,
    default: new Date(),
    type: Date,
  },
  deletedAt: {
    default: null,
    type: Date,
  },
};

const attibuteSchema = new mongoose.Schema(
  {
    attributeName: {
      required: true,
      type: String,
    },
    ...baseSchema,
  },
  { strict: false, versionKey: false },
);

const propertySchema = new mongoose.Schema(
  {
    propertyName: {
      required: true,
      type: String,
    },
    status: {
      required: true,
      type: String,
      default: 'active',
    },
    ...baseSchema,
  },
  { strict: false, versionKey: false },
);

const attibutePropertiesSchema = new mongoose.Schema(
  {
    attributeId: {
      required: true,
      type: String,
    },
    propertyId: {
      required: true,
      type: String,
    },
  },
  { strict: false, versionKey: false },
);

const productSchema = new mongoose.Schema(
  {
    productName: {
      required: true,
      type: String,
    },
    productCode: {
      required: true,
      type: String,
    },
    productType: {
      required: true,
      default: 'single',
      type: String,
    },
    parentId: {
      type: String,
    },
    categoryId: {
      type: String,
    },
    sellingPrice: {
      required: true,
      default: 0,
      type: Number,
    },
    listedPrice: {
      required: true,
      default: 0,
      type: Number,
    },
    attributes: {
      default: {},
      required: true,
      type: Object,
    },
    status: {
      required: true,
      type: String,
      default: 'active',
    },
    description: {
      required: false,
      type: String,
    },
    images: {
      default: {},
      required: true,
      type: Object,
    },
    slug: {
      required: true,
      default: '',
      type: String,
    },
    ...baseSchema,
  },
  { strict: false, versionKey: false },
);

const cateogorySchema = new mongoose.Schema(
  {
    categoryName: {
      required: true,
      type: String,
    },
    slug: {
      required: true,
      type: String,
    },
    parentId: {
      default: null,
      type: String,
    },
    categoryType: {
      required: true,
      type: String,
      default: 'products',
    },
    status: {
      required: true,
      type: String,
      default: 'active',
    },
    ...baseSchema,
  },
  { strict: false, versionKey: false },
);

const schemaExcel = {
  'Tên sản phẩm*': {
    prop: 'productParentName',
    type: String,
  },

  'Loại sản phẩm': {
    prop: 'categoryName',
    type: String,
  },
  'Thuộc tính 1': {
    prop: 'attributeName1',
    type: String,
  },
  'Giá trị thuộc tính 1': {
    prop: 'propertyName1',
    type: String,
  },
  'Thuộc tính 2': {
    prop: 'attributeName2',
    type: String,
  },
  'Giá trị thuộc tính 2': {
    prop: 'propertyName2',
    type: String,
  },
  'Thuộc tính 3': {
    prop: 'attributeName3',
    type: String,
  },
  'Giá trị thuộc tính 3': {
    prop: 'propertyName3',
    type: String,
  },
  'Tên phiên bản sản phẩm': {
    prop: 'productName',
    type: String,
  },
  'Mã SKU*': {
    prop: 'productCode',
    type: String,
  },
  Barcode: {
    prop: 'productCode',
    type: String,
  },
  'Ảnh đại diện': {
    prop: 'image',
    type: String,
  },
  'PL_Giá bán lẻ': {
    prop: 'sellingPrice',
    type: Number,
  },
};
module.exports = {
  attibuteSchema,
  productSchema,
  propertySchema,
  cateogorySchema,
  attibutePropertiesSchema,
  schemaExcel,
};
