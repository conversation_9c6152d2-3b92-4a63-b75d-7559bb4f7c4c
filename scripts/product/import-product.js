/* eslint-disable @typescript-eslint/no-var-requires */
require('dotenv').config();
const path = require('path');
const readXlsxFile = require('read-excel-file/node');
const mongoose = require('mongoose');
const {
  formatString,
  getParentCode,
  stringToSlug,
  newObjectId,
} = require('./util');
const {
  propertySchema,
  attibuteSchema,
  productSchema,
  cateogorySchema,
  attibutePropertiesSchema,
  schemaExcel,
} = require('./schema');

const Property = mongoose.model('properties', propertySchema);
const Attribute = mongoose.model('attributes', attibuteSchema);
const Product = mongoose.model('products', productSchema);
const Category = mongoose.model('categories', cateogorySchema);
const AttributeProperty = mongoose.model(
  'attribute-properties',
  attibutePropertiesSchema,
);

const handleProperty = async (rows) => {
  const properties1 = rows.map((row) => row.propertyName1);
  const properties2 = rows.map((row) => row.propertyName2);
  const properties3 = rows.map((row) => row.propertyName3);

  const properties = [...new Set(properties1.concat(properties2, properties3))];
  const bulkData = properties.reduce((result, item) => {
    if (!item) return result;
    result.push({
      updateOne: {
        filter: {
          propertyName: item,
        },
        update: {
          $set: {
            propertyName: item,
          },
        },
        upsert: true,
      },
    });
    return result;
  }, []);
  await Property.bulkWrite(bulkData);
};

const handleAttribute = async (rows) => {
  const attributes1 = rows.map((row) => row.attributeName1);
  const attributes2 = rows.map((row) => row.attributeName2);
  const attributes3 = rows.map((row) => row.attributeName3);
  const attributes = [...new Set(attributes1.concat(attributes2, attributes3))];

  const bulkData = attributes.reduce((result, item) => {
    if (!item) return result;
    result.push({
      updateOne: {
        filter: {
          attributeName: item,
        },
        update: {
          $set: {
            attributeName: item,
          },
        },
        upsert: true,
      },
    });
    return result;
  }, []);
  await Attribute.bulkWrite(bulkData);
};

const handleAttributeProperty = async (rows) => {
  const mapping = {};

  for (const product of rows) {
    const isNewParent = product.productParentName ? true : false;
    if (isNewParent) {
      await loopToInsertAttributeProperty(product, product, mapping);
    } else {
      await loopToInsertAttributeProperty(product, mapping, mapping);
    }
  }
};

const loopToInsertAttributeProperty = async (product, common, mapping) => {
  for (let i = 1; i < 4; i++) {
    if (common[`attributeName${i}`] && product[`propertyName${i}`]) {
      const upsert = await createAttributeProperty(
        common[`attributeName${i}`],
        product[`propertyName${i}`],
      );
      if (!upsert) {
        throw new Error('Can not upsert attribute property ');
      }
      mapping[`attributeName${i}`] = common[`attributeName${i}`] || null;
    }
  }
};

const createAttributeProperty = async (attributeName, propertyName) => {
  const [attribute, property] = await Promise.all([
    Attribute.findOne({
      attributeName: attributeName,
    }),
    Property.findOne({
      propertyName: propertyName,
    }),
  ]);
  if (attribute && property) {
    const upsert = await AttributeProperty.updateOne(
      {
        attributeId: attribute._id,
        propertyId: property._id,
      },
      {
        $set: {
          attributeId: attribute._id,
          propertyId: property._id,
        },
      },
      { upsert: true, new: true },
    );
    return upsert;
  }
  return null;
};

const createProductDb = (product, parentId) => {
  const productName = product.productParentName || product.productName;
  return Product.updateOne(
    {
      productCode: product.productCode,
    },
    {
      $set: {
        productName: productName,
        productCode: product.productCode,
        productType: 'child',
        images: product.image
          ? [
              {
                id: newObjectId(),
                url: product.image,
                position: 'product-01',
              },
            ]
          : [],
        sellingPrice: product.sellingPrice,
        parentId: String(parentId),
        categoryId: product.categoryId,
        attributes: product.attributes,
        slug: stringToSlug(productName),
      },
    },
    { upsert: true },
  );
};

const createProductParentDb = (product) => {
  const parentCode = getParentCode(product.productCode);
  return Product.findOneAndUpdate(
    {
      productCode: parentCode,
    },
    {
      $set: {
        productName: product.productParentName,
        productCode: parentCode,
        productType: 'parent',
        images: product.image
          ? [
              {
                id: newObjectId(),
                url: product.image,
                position: 'product-01',
              },
            ]
          : [],
        sellingPrice: 0,
        parentId: null,
        categoryId: product.categoryId,
        slug: stringToSlug(product.productParentName),
      },
    },
    { new: true, upsert: true },
  );
};

const handleAttributeProduct = async (product, common, hashmap) => {
  for (let i = 1; i < 4; i++) {
    if (common[`attributeName${i}`] && product[`propertyName${i}`]) {
      const [attribute, property] = await Promise.all([
        Attribute.findOne({
          attributeName: common[`attributeName${i}`],
        }),
        Property.findOne({
          propertyName: product[`propertyName${i}`],
        }),
      ]);
      hashmap[attribute._id] = {
        id: String(attribute._id),
        properties: {
          [property._id]: String(property._id),
        },
      };
    }
  }
  return hashmap;
};
const handleProduct = async (rows) => {
  let parentId = '';
  let categoryId = '';
  let hashmap = {};
  let mapping = {};

  for (const product of rows) {
    const isNewParent = product.productParentName ? true : false;
    if (isNewParent) {
      categoryId = await getCategoryIdByName(product.categoryName);
      product.categoryId = categoryId;
      const productParentCreated = await createProductParentDb(product);
      parentId = productParentCreated._id;
      mapping = {
        attributeName1: product.attributeName1 || null,
        attributeName2: product.attributeName2 || null,
        attributeName3: product.attributeName3 || null,
      };
      hashmap = {};
    }

    const attributes = await handleAttributeProduct(product, mapping, hashmap);
    product.attributes = attributes;
    product.categoryId = categoryId;
    await createProductDb(product, parentId);
  }
};

const getCategoryIdByName = async (name) => {
  const category = await Category.findOne({
    categoryName: name,
  });
  return category ? category._id : null;
};

const handleCategory = async (rows) => {
  const categories = [
    ...new Set(
      rows.filter((row) => row.categoryName).map((row) => row.categoryName),
    ),
  ];
  const bulkData = categories.reduce((result, item) => {
    if (!item) return result;
    const slug = `${formatString(`${item.replace(/\s/g, '-')}`)}`;
    result.push({
      updateOne: {
        filter: {
          categoryName: item,
          slug: slug,
        },
        update: {
          $set: {
            attributeName: item,
            slug: slug,
          },
        },
        upsert: true,
      },
    });
    return result;
  }, []);
  await Category.bulkWrite(bulkData);
};

const handleAttributeParent = async (rows) => {
  const parents = rows
    .filter((row) => row.productParentName)
    .map((row) => getParentCode(row.productCode));
  const propducts = await Product.find({
    productCode: {
      $in: parents,
    },
  }).exec();
  const productsJson = propducts.map((product) => product.toJSON());

  for (const product of productsJson) {
    const childs = await Product.find({
      parentId: String(product._id),
    }).exec();
    if (childs.length) {
      const childsJson = childs.map((product) => product.toJSON());
      const attributes = childsJson.reduce((result, product) => {
        product.attributes &&
          Object.values(product.attributes).map((attribute) => {
            if (result[attribute.id]) {
              result[attribute.id].properties = {
                ...result[attribute.id].properties,
                ...attribute.properties,
              };
            } else {
              result[attribute.id] = {
                properties: attribute.properties,
              };
            }
          });
        return result;
      }, {});
      await Product.findOneAndUpdate(
        {
          _id: product._id,
        },
        {
          $set: {
            attributes,
          },
        },
      );
    }
  }
};

const importProductFromExcel = () => {
  return readXlsxFile(path.join(__dirname, './sanpham.xlsx'), {
    schema: schemaExcel,
  }).then(async ({ rows, errors }) => {
    if (errors && errors.length) {
      console.log(errors);
      return;
    }

    try {
      const uri = process.env.DB_URL;
      mongoose.connect(uri, {
        useUnifiedTopology: true,
        auth: {
          username: process.env.DB_USER,
          password: process.env.DB_PASS,
        },
      });

      await handleProperty(rows);
      await handleAttribute(rows);
      await handleAttributeProperty(rows);
      await handleCategory(rows);
      await handleProduct(rows);
      await handleAttributeParent(rows);
    } catch (error) {
      console.log(error);
      process.exit(1);
    } finally {
      await mongoose.connection.close();
    }
  });
};
importProductFromExcel();
