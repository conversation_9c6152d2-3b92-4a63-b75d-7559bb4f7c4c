/* eslint-disable @typescript-eslint/no-var-requires */
require('dotenv').config();

const mongoose = require('mongoose');
const { formatStringWithDash } = require('./util');
const { productSchema } = require('./schema');

const Product = mongoose.model('products', productSchema);

(async () => {
  const uri = process.env.DB_URL;
  mongoose.connect(uri, {
    useUnifiedTopology: true,
    auth: {
      username: process.env.DB_USER,
      password: process.env.DB_PASS,
    },
  });

  try {
    const propducts = await Product.find({}).exec();
    const productsJson = propducts.map((product) => product.toJSON());
    for (const product of productsJson) {
      await Product.findOneAndUpdate(
        {
          _id: product._id,
        },
        {
          $set: {
            slug: formatStringWithDash(product.productName),
          },
        },
      );
    }
  } catch (e) {
    console.error(e);
  } finally {
    console.log('Done');
    await mongoose.connection.close();
  }
})();
