/* eslint-disable @typescript-eslint/no-var-requires */
require('dotenv').config();
const path = require('path');
const fs = require('fs');
const mongoose = require('mongoose');

(async () => {
  const uri = process.env.DB_URL;
  mongoose.connect(uri, {
    useUnifiedTopology: true,
    auth: {
      username: process.env.DB_USER,
      password: process.env.DB_PASS,
    },
  });

  try {
    const wardVNSchema = new mongoose.Schema(
      {
        code: {
          index: true,
          unique: true,
          type: String,
        },
        name: {
          type: String,
        },
        districtCode: {
          index: true,
          type: String,
        },
      },
      { strict: false, versionKey: false },
    );
    const Ward = mongoose.model('vn-wards', wardVNSchema);

    const districtVNSchema = new mongoose.Schema(
      {
        code: {
          index: true,
          unique: true,
          type: String,
        },
        name: {
          type: String,
        },
        cityCode: {
          index: true,
          type: String,
        },
      },
      { strict: false, versionKey: false },
    );
    const District = mongoose.model('vn-districts', districtVNSchema);

    const cityVNSchema = new mongoose.Schema(
      {
        code: {
          index: true,
          unique: true,
          type: String,
        },
        name: {
          type: String,
        },
      },
      { strict: false, versionKey: false },
    );
    const City = mongoose.model('vn-cities', cityVNSchema);

    const locationsRaw = fs.readFileSync(
      path.join(__dirname, './mongo_data_vn_unit.json'),
      {
        encoding: 'utf8',
      },
    );
    const locationJson = JSON.parse(locationsRaw);
    for (const city of locationJson) {
      for (const district of city['District']) {
        for (const ward of district['Ward']) {
          await Ward.create({
            code: ward['Code'],
            name: ward['Name'],
            districtCode: ward['DistrictCode'],
          });
        }
        await District.create({
          code: district['Code'],
          name: district['Name'],
          cityCode: district['ProvinceCode'],
        });
      }
      await City.create({
        code: city['Code'],
        name: city['Name'],
      });
    }
  } catch (e) {
    console.error(e);
  } finally {
    console.log('Done');
    await mongoose.connection.close();
  }
})();
