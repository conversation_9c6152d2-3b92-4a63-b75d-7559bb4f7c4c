/* eslint-disable @typescript-eslint/no-var-requires */
require('dotenv').config();
const path = require('path');
const fs = require('fs');
const mongoose = require('mongoose');

(async () => {
  const uri = process.env.DB_URL;
  mongoose.connect(uri, {
    useUnifiedTopology: true,
    auth: {
      username: process.env.DB_USER,
      password: process.env.DB_PASS,
    },
  });

  try {
    const globalCountrySchema = new mongoose.Schema(
      {
        code: {
          index: true,
          unique: true,
          type: String,
        },
        name: {
          type: String,
        },
        latitude: {
          type: Number,
        },
        longitude: {
          type: Number,
        },
      },
      { strict: false, versionKey: false },
    );
    const GlobalCountry = mongoose.model(
      'global-countries',
      globalCountrySchema,
    );

    const globalStateSchema = new mongoose.Schema(
      {
        code: {
          index: true,
          unique: true,
          type: String,
        },
        name: {
          type: String,
        },
        countryCode: {
          index: true,
          type: String,
        },
        latitude: {
          type: Number,
        },
        longitude: {
          type: Number,
        },
      },
      { strict: false, versionKey: false },
    );
    const GlobalState = mongoose.model('global-states', globalStateSchema);

    const globalCitySchema = new mongoose.Schema(
      {
        code: {
          index: true,
          unique: true,
          type: String,
        },
        name: {
          type: String,
        },
        stateCode: {
          index: true,
          type: String,
        },
        latitude: {
          type: Number,
        },
        longitude: {
          type: Number,
        },
      },
      { strict: false, versionKey: false },
    );
    const GlobalCity = mongoose.model('global-cities', globalCitySchema);

    const countriesRaw = fs.readFileSync(
      path.join(
        __dirname,
        '../../libs/wstore-location/json/countries+states+cities.json',
      ),
      {
        encoding: 'utf8',
      },
    );
    const countriesJson = JSON.parse(countriesRaw);
    for (const country of countriesJson) {
      await GlobalCountry.create({
        code: country['id'],
        name: country['name'],
        latitude: Number(country['latitude']),
        longitude: Number(country['longitude']),
      });
      const states = country['states'];
      for (const state of states) {
        await GlobalState.create({
          code: state['id'],
          name: state['name'],
          countryCode: country['id'],
          latitude: Number(state['latitude']),
          longitude: Number(state['longitude']),
        });
        const cities = state['cities'];
        for (const city of cities) {
          await GlobalCity.create({
            code: city['id'],
            name: city['name'],
            stateCode: state['id'],
            latitude: Number(city['latitude']),
            longitude: Number(city['longitude']),
          });
        }
      }
    }

    console.log('Done');
  } catch (e) {
    console.error(e);
  } finally {
    console.log('Done');
    await mongoose.connection.close();
  }
})();
