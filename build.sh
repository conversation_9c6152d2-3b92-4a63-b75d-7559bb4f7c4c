#!/bin/bash
git pull origin dev

# Install dependencies using pnpm
echo "Installing dependencies with pnpm..."
pnpm install --frozen-lockfile

# Build the Next.js project
echo "Building Next.js project..."
pnpm run build

# Reload the first pm2 instance (position 0)
echo "Reloading pm2 instance at position 0..."
pm2 reload ws-admin-be

# Optional: Log completion
echo "Build and pm2 reload completed!"
