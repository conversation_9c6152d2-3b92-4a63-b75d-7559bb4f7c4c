# version: '3.8'
# services:
#   mongodb:
#     image: mongo:6
#     ports:
#       - '27017:27017'
#     volumes:
#       - dbdata6:/data/db
# volumes:
#   dbdata6:

version: '3.8'

services:
  mongodb:
    image: mongo:6
    container_name: mongodb
    restart: unless-stopped
    ports:
      - '27017:27017'
    environment:
      MONGO_INITDB_ROOT_USERNAME: root # Tạo user "root"
      MONGO_INITDB_ROOT_PASSWORD: root # Mật khẩu "root"
      MONGO_INITDB_DATABASE: wstore # Database mặc định là "wstore"
    volumes:
      - mongo_data:/data/db

volumes:
  mongo_data:
    driver: local
